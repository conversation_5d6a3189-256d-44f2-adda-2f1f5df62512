{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🏷️ UI Element Technical Labeling System\n", "\n", "## Intelligent Technical Labeling for Landing Page UI Elements\n", "\n", "This notebook implements a **comprehensive technical labeling system** that analyzes UI elements from coordinates and DOM data to generate meaningful technical labels based on the landing page structure:\n", "\n", "### 🔄 **Technical Labeling Workflow:**\n", "1. **Data Loading**: Load UI coordinates and comprehensive DOM element information\n", "2. **Element Mapping**: Map coordinate data to corresponding DOM elements\n", "3. **Technical Analysis**: Extract semantic information from HTML structure, CSS classes, and attributes\n", "4. **Label Generation**: Create technical labels based on element purpose and functionality\n", "5. **Contextual Naming**: Generate labels that reflect the element's role in the landing page\n", "6. **Comprehensive Output**: Provide detailed technical specifications for each UI element\n", "\n", "### 🏗️ **Technical Label Components:**\n", "1. ✅ **HTML Semantic Tags** - video, h1, div, button, etc.\n", "2. ✅ **CSS Class Analysis** - Extract meaningful class names and patterns\n", "3. ✅ **Functional Purpose** - Determine element's role (hero video, heading, icon, etc.)\n", "4. ✅ **Content Analysis** - Use text content to understand element purpose\n", "5. ✅ **Attribute Inspection** - Analyze HTML attributes for additional context\n", "6. ✅ **Position Context** - Consider element placement and hierarchy\n", "7. ✅ **Landing Page Structure** - Map elements to common landing page sections\n", "\n", "### 🎯 **Key Features:**\n", "- ✅ **Automated Technical Labeling**: Generate precise technical names for UI elements\n", "- ✅ **DOM-Based Analysis**: Use comprehensive DOM data for accurate labeling\n", "- ✅ **Semantic Understanding**: Extract meaning from HTML structure and CSS classes\n", "- ✅ **Landing Page Context**: Labels reflect common landing page patterns\n", "- ✅ **Detailed Specifications**: Provide complete technical information for each element\n", "- ✅ **Coordinate Mapping**: Link visual coordinates to technical specifications\n", "- ✅ **Extensible Framework**: Easy to add new labeling rules and patterns\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🚀 Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages for technical labeling system\n", "!pip install -q pandas\n", "!pip install -q numpy\n", "!pip install -q matplotlib\n", "!pip install -q seaborn\n", "!pip install -q ipywidgets\n", "\n", "print(\"✅ Packages for technical labeling system installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## 📦 Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import_libraries"}, "outputs": [], "source": ["import json\n", "import re\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from typing import Dict, List, Any, Optional, Tuple\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from IPython.display import display, HTML, clear_output\n", "import ipywidgets as widgets\n", "from collections import defaultdict\n", "\n", "print(\"✅ Libraries for technical labeling system imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## 📊 Data Loading and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data"}, "outputs": [], "source": ["class DataLoader:\n", "    \"\"\"Load and validate UI element data from JSON files\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.coordinates_data = None\n", "        self.element_info_data = None\n", "        self.loaded = False\n", "    \n", "    def load_files(self, coordinates_path: str = \"coordinates.json\", \n", "                   element_info_path: str = \"element_info.json\") -> bool:\n", "        \"\"\"Load data from JSON files\"\"\"\n", "        try:\n", "            # Load coordinates data\n", "            with open(coordinates_path, 'r') as f:\n", "                self.coordinates_data = json.load(f)\n", "            \n", "            # Load element info data\n", "            with open(element_info_path, 'r') as f:\n", "                self.element_info_data = json.load(f)\n", "            \n", "            self.loaded = True\n", "            print(f\"✅ Data loaded successfully!\")\n", "            print(f\"📍 Coordinates: {len(self.coordinates_data)} elements\")\n", "            print(f\"🏗️ Element info: {len(self.element_info_data)} elements\")\n", "            \n", "            return True\n", "            \n", "        except FileNotFoundError as e:\n", "            print(f\"❌ File not found: {e}\")\n", "            return False\n", "        except json.JSONDecodeError as e:\n", "            print(f\"❌ Invalid JSON format: {e}\")\n", "            return False\n", "        except Exception as e:\n", "            print(f\"❌ Error loading data: {e}\")\n", "            return False\n", "    \n", "    def validate_data(self) -> bool:\n", "        \"\"\"Validate the loaded data structure\"\"\"\n", "        if not self.loaded:\n", "            print(\"❌ No data loaded. Please load data first.\")\n", "            return False\n", "        \n", "        # Validate coordinates data\n", "        if not isinstance(self.coordinates_data, list):\n", "            print(\"❌ Coordinates data should be a list\")\n", "            return False\n", "        \n", "        # Validate element info data\n", "        if not isinstance(self.element_info_data, dict):\n", "            print(\"❌ Element info data should be a dictionary\")\n", "            return False\n", "        \n", "        print(\"✅ Data validation passed!\")\n", "        return True\n", "    \n", "    def get_data_summary(self) -> Dict[str, Any]:\n", "        \"\"\"Get summary of loaded data\"\"\"\n", "        if not self.loaded:\n", "            return {\"error\": \"No data loaded\"}\n", "        \n", "        summary = {\n", "            \"coordinates_count\": len(self.coordinates_data),\n", "            \"element_info_count\": len(self.element_info_data),\n", "            \"coordinate_labels\": [item.get(\"label\", \"Unknown\") for item in self.coordinates_data],\n", "            \"element_tags\": [info.get(\"tag\", \"unknown\") for info in self.element_info_data.values()]\n", "        }\n", "        \n", "        return summary\n", "\n", "# Initialize data loader\n", "data_loader = DataLoader()\n", "print(\"✅ DataLoader initialized!\")"]}, {"cell_type": "markdown", "metadata": {"id": "technical_labeler"}, "source": ["## 🏷️ Technical Labeling Engine"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "labeling_engine"}, "outputs": [], "source": ["class TechnicalLabeler:\n", "    \"\"\"Generate technical labels for UI elements based on DOM data and coordinates\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.landing_page_patterns = {\n", "            'hero': ['hero', 'banner', 'main', 'primary'],\n", "            'navigation': ['nav', 'menu', 'header', 'navigation'],\n", "            'content': ['content', 'main', 'article', 'section'],\n", "            'footer': ['footer', 'bottom', 'end'],\n", "            'sidebar': ['sidebar', 'aside', 'secondary'],\n", "            'form': ['form', 'input', 'submit', 'contact'],\n", "            'media': ['video', 'image', 'img', 'media'],\n", "            'button': ['button', 'btn', 'cta', 'action'],\n", "            'heading': ['heading', 'title', 'h1', 'h2', 'h3'],\n", "            'icon': ['icon', 'symbol', 'graphic']\n", "        }\n", "        \n", "        self.semantic_tags = {\n", "            'video': 'Video Element',\n", "            'h1': 'Primary Heading',\n", "            'h2': 'Secondary Heading',\n", "            'h3': 'Tertiary Heading',\n", "            'h4': 'Quaternary Heading',\n", "            'h5': '<PERSON><PERSON><PERSON>',\n", "            'h6': '<PERSON><PERSON> Heading',\n", "            'button': 'Interactive Button',\n", "            'a': '<PERSON>',\n", "            'img': 'Image Element',\n", "            'div': 'Container Element',\n", "            'span': 'Inline Element',\n", "            'p': 'Paragraph Element',\n", "            'nav': 'Navigation Element',\n", "            'header': 'Header Section',\n", "            'footer': 'Footer Section',\n", "            'main': 'Main Content',\n", "            'section': 'Content Section',\n", "            'article': 'Article Content',\n", "            'aside': 'Sidebar Content',\n", "            'form': 'Form Element',\n", "            'input': 'Input Field',\n", "            'textarea': 'Text Area',\n", "            'select': 'Dropdown Select',\n", "            'label': 'Form Label',\n", "            'ul': 'Unordered List',\n", "            'ol': 'Ordered List',\n", "            'li': 'List Item'\n", "        }\n", "    \n", "    def extract_css_context(self, css_classes: List[str]) -> Dict[str, Any]:\n", "        \"\"\"Extract meaningful context from CSS class names\"\"\"\n", "        context = {\n", "            'section_type': None,\n", "            'component_type': None,\n", "            'style_modifiers': [],\n", "            'semantic_hints': []\n", "        }\n", "        \n", "        for css_class in css_classes:\n", "            class_lower = css_class.lower()\n", "            \n", "            # Check for landing page section patterns\n", "            for section, patterns in self.landing_page_patterns.items():\n", "                if any(pattern in class_lower for pattern in patterns):\n", "                    if not context['section_type']:\n", "                        context['section_type'] = section\n", "                    context['semantic_hints'].append(section)\n", "            \n", "            # Extract component type hints\n", "            if 'video' in class_lower:\n", "                context['component_type'] = 'video'\n", "            elif 'heading' in class_lower or any(h in class_lower for h in ['h1', 'h2', 'h3', 'title']):\n", "                context['component_type'] = 'heading'\n", "            elif 'button' in class_lower or 'btn' in class_lower:\n", "                context['component_type'] = 'button'\n", "            elif 'icon' in class_lower:\n", "                context['component_type'] = 'icon'\n", "            elif 'image' in class_lower or 'img' in class_lower:\n", "                context['component_type'] = 'image'\n", "            \n", "            # Extract style modifiers\n", "            style_keywords = ['primary', 'secondary', 'large', 'small', 'bold', 'light', 'dark']\n", "            for keyword in style_keywords:\n", "                if keyword in class_lower:\n", "                    context['style_modifiers'].append(keyword)\n", "        \n", "        return context\n", "    \n", "    def analyze_element_attributes(self, attributes: Dict[str, Any]) -> Dict[str, Any]:\n", "        \"\"\"Analyze HTML attributes for additional context\"\"\"\n", "        analysis = {\n", "            'interactive': <PERSON><PERSON><PERSON>,\n", "            'media_type': None,\n", "            'accessibility': {},\n", "            'functionality': []\n", "        }\n", "        \n", "        # Check for interactive attributes\n", "        interactive_attrs = ['onclick', 'href', 'type', 'role']\n", "        for attr in interactive_attrs:\n", "            if attr in attributes:\n", "                analysis['interactive'] = True\n", "                analysis['functionality'].append(f'has_{attr}')\n", "        \n", "        # Check for media attributes\n", "        if 'src' in attributes:\n", "            src = attributes['src'].lower()\n", "            if any(ext in src for ext in ['.mp4', '.webm', '.avi']):\n", "                analysis['media_type'] = 'video'\n", "            elif any(ext in src for ext in ['.jpg', '.png', '.gif', '.svg']):\n", "                analysis['media_type'] = 'image'\n", "        \n", "        # Check accessibility attributes\n", "        accessibility_attrs = ['alt', 'aria-label', 'title', 'role']\n", "        for attr in accessibility_attrs:\n", "            if attr in attributes:\n", "                analysis['accessibility'][attr] = attributes[attr]\n", "        \n", "        return analysis\n", "    \n", "    def generate_technical_label(self, coordinate_data: Dict[str, Any], \n", "                                element_data: Dict[str, Any]) -> Dict[str, Any]:\n", "        \"\"\"Generate comprehensive technical label for a UI element\"\"\"\n", "        \n", "        # Extract basic information\n", "        tag = element_data.get('tag', 'unknown')\n", "        css_classes = element_data.get('classes', [])\n", "        attributes = element_data.get('attributes', {})\n", "        text_content = element_data.get('text', '').strip()\n", "        coordinates = coordinate_data.get('coordinates', {})\n", "        original_label = coordinate_data.get('label', 'Unknown')\n", "        \n", "        # Analyze CSS context\n", "        css_context = self.extract_css_context(css_classes)\n", "        \n", "        # Analyze attributes\n", "        attr_analysis = self.analyze_element_attributes(attributes)\n", "        \n", "        # Generate semantic tag description\n", "        semantic_description = self.semantic_tags.get(tag, f'{tag.upper()} Element')\n", "        \n", "        # Generate technical label based on analysis\n", "        technical_label = self._construct_technical_label(\n", "            tag, css_context, attr_analysis, text_content, semantic_description\n", "        )\n", "        \n", "        # Create comprehensive result\n", "        result = {\n", "            'original_label': original_label,\n", "            'technical_label': technical_label,\n", "            'semantic_description': semantic_description,\n", "            'html_tag': tag,\n", "            'css_classes': css_classes,\n", "            'css_context': css_context,\n", "            'attributes_analysis': attr_analysis,\n", "            'text_content': text_content[:100] + '...' if len(text_content) > 100 else text_content,\n", "            'coordinates': coordinates,\n", "            'element_size': {\n", "                'width': coordinates.get('width', 0),\n", "                'height': coordinates.get('height', 0),\n", "                'area': coordinates.get('width', 0) * coordinates.get('height', 0)\n", "            },\n", "            'position': {\n", "                'x': coordinates.get('x', 0),\n", "                'y': coordinates.get('y', 0)\n", "            }\n", "        }\n", "        \n", "        return result\n", "    \n", "    def _construct_technical_label(self, tag: str, css_context: Dict[str, Any], \n", "                                 attr_analysis: Dict[str, Any], text_content: str,\n", "                                 semantic_description: str) -> str:\n", "        \"\"\"Construct the final technical label\"\"\"\n", "        \n", "        # Start with semantic description\n", "        label_parts = []\n", "        \n", "        # Add section context if available\n", "        if css_context['section_type']:\n", "            label_parts.append(css_context['section_type'].title())\n", "        \n", "        # Add component type if different from tag\n", "        if css_context['component_type'] and css_context['component_type'] != tag:\n", "            label_parts.append(css_context['component_type'].title())\n", "        \n", "        # Add semantic description\n", "        label_parts.append(semantic_description)\n", "        \n", "        # Add media type if applicable\n", "        if attr_analysis['media_type']:\n", "            if attr_analysis['media_type'] not in ' '.join(label_parts).lower():\n", "                label_parts.insert(-1, attr_analysis['media_type'].title())\n", "        \n", "        # Add interactivity indicator\n", "        if attr_analysis['interactive']:\n", "            label_parts.append('(Interactive)')\n", "        \n", "        # Add style modifiers\n", "        if css_context['style_modifiers']:\n", "            modifiers = ', '.join(css_context['style_modifiers']).title()\n", "            label_parts.append(f'[{modifiers}]')\n", "        \n", "        # Construct final label\n", "        technical_label = ' '.join(label_parts)\n", "        \n", "        # Clean up and format\n", "        technical_label = re.sub(r'\\s+', ' ', technical_label).strip()\n", "        \n", "        return technical_label\n", "\n", "# Initialize technical labeler\n", "technical_labeler = TechnicalLabeler()\n", "print(\"✅ TechnicalL<PERSON>ler initialized!\")"]}, {"cell_type": "markdown", "metadata": {"id": "load_and_process"}, "source": ["## 📥 Load Data and Generate Technical Labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "process_data"}, "outputs": [], "source": ["# Load the data\n", "print(\"📊 Loading UI element data...\")\n", "if data_loader.load_files():\n", "    if data_loader.validate_data():\n", "        print(\"\\n🔄 Processing elements and generating technical labels...\")\n", "        \n", "        # Process each coordinate entry\n", "        labeled_elements = []\n", "        \n", "        for i, coord_data in enumerate(data_loader.coordinates_data):\n", "            # Find corresponding element info\n", "            element_key = f\"element_{i + 1}\"\n", "            element_data = data_loader.element_info_data.get(element_key, {})\n", "            \n", "            if element_data:\n", "                # Generate technical label\n", "                technical_info = technical_labeler.generate_technical_label(coord_data, element_data)\n", "                technical_info['index'] = i + 1\n", "                technical_info['element_key'] = element_key\n", "                labeled_elements.append(technical_info)\n", "                \n", "                print(f\"✅ Processed element {i + 1}: {technical_info['technical_label']}\")\n", "            else:\n", "                print(f\"⚠️ No element data found for coordinate entry {i + 1}\")\n", "        \n", "        print(f\"\\n🎉 Successfully processed {len(labeled_elements)} elements!\")\n", "        \n", "        # Display summary\n", "        print(\"\\n📋 TECHNICAL LABELING SUMMARY:\")\n", "        print(\"=\" * 50)\n", "        for element in labeled_elements:\n", "            print(f\"🏷️ {element['index']}: {element['technical_label']}\")\n", "            print(f\"   📍 Original: {element['original_label']}\")\n", "            print(f\"   🏗️ HTML: <{element['html_tag']}>\")\n", "            print(f\"   📐 Size: {element['element_size']['width']}x{element['element_size']['height']}px\")\n", "            if element['text_content']:\n", "                print(f\"   📝 Content: {element['text_content'][:50]}{'...' if len(element['text_content']) > 50 else ''}\")\n", "            print()\n", "    else:\n", "        print(\"❌ Data validation failed\")\n", "else:\n", "    print(\"❌ Failed to load data files\")"]}, {"cell_type": "markdown", "metadata": {"id": "detailed_analysis"}, "source": ["## 🔍 Detailed Element Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "detailed_analysis_code"}, "outputs": [], "source": ["# Create detailed analysis if elements were processed successfully\n", "if 'labeled_elements' in locals() and labeled_elements:\n", "    print(\"🔍 DETAILED TECHNICAL ANALYSIS\")\n", "    print(\"=\" * 60)\n", "    \n", "    for element in labeled_elements:\n", "        print(f\"\\n🏷️ ELEMENT {element['index']}: {element['technical_label']}\")\n", "        print(\"-\" * 40)\n", "        \n", "        # Basic Information\n", "        print(f\"📍 Original Label: {element['original_label']}\")\n", "        print(f\"🏗️ HTML Tag: <{element['html_tag']}>\")\n", "        print(f\"📐 Dimensions: {element['element_size']['width']}×{element['element_size']['height']}px\")\n", "        print(f\"📍 Position: ({element['position']['x']}, {element['position']['y']})\")\n", "        print(f\"📏 Area: {element['element_size']['area']:,}px²\")\n", "        \n", "        # CSS Classes\n", "        if element['css_classes']:\n", "            print(f\"🎨 CSS Classes: {', '.join(element['css_classes'])}\")\n", "        \n", "        # CSS Context Analysis\n", "        css_context = element['css_context']\n", "        if css_context['section_type']:\n", "            print(f\"🏢 Section Type: {css_context['section_type'].title()}\")\n", "        if css_context['component_type']:\n", "            print(f\"🧩 Component Type: {css_context['component_type'].title()}\")\n", "        if css_context['style_modifiers']:\n", "            print(f\"✨ Style Modifiers: {', '.join(css_context['style_modifiers']).title()}\")\n", "        if css_context['semantic_hints']:\n", "            print(f\"💡 Semantic Hints: {', '.join(set(css_context['semantic_hints'])).title()}\")\n", "        \n", "        # Attributes Analysis\n", "        attr_analysis = element['attributes_analysis']\n", "        if attr_analysis['interactive']:\n", "            print(f\"🖱️ Interactive: Yes\")\n", "            if attr_analysis['functionality']:\n", "                print(f\"⚙️ Functionality: {', '.join(attr_analysis['functionality'])}\")\n", "        else:\n", "            print(f\"🖱️ Interactive: No\")\n", "        \n", "        if attr_analysis['media_type']:\n", "            print(f\"🎬 Media Type: {attr_analysis['media_type'].title()}\")\n", "        \n", "        if attr_analysis['accessibility']:\n", "            print(f\"♿ Accessibility Attributes:\")\n", "            for attr, value in attr_analysis['accessibility'].items():\n", "                print(f\"   • {attr}: {value[:50]}{'...' if len(str(value)) > 50 else ''}\")\n", "        \n", "        # Text Content\n", "        if element['text_content']:\n", "            print(f\"📝 Text Content: {element['text_content']}\")\n", "        \n", "        print()\n", "else:\n", "    print(\"⚠️ No labeled elements available for detailed analysis\")"]}, {"cell_type": "markdown", "metadata": {"id": "export_results"}, "source": ["## 📤 Export Technical Labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "export_code"}, "outputs": [], "source": ["# Export results to different formats\n", "if 'labeled_elements' in locals() and labeled_elements:\n", "    \n", "    # Create DataFrame for easy manipulation\n", "    export_data = []\n", "    for element in labeled_elements:\n", "        export_row = {\n", "            'Index': element['index'],\n", "            'Technical_Label': element['technical_label'],\n", "            'Original_Label': element['original_label'],\n", "            'HTML_Tag': element['html_tag'],\n", "            'Semantic_Description': element['semantic_description'],\n", "            'Width': element['element_size']['width'],\n", "            'Height': element['element_size']['height'],\n", "            'Area': element['element_size']['area'],\n", "            'X_Position': element['position']['x'],\n", "            'Y_Position': element['position']['y'],\n", "            'CSS_Classes': ', '.join(element['css_classes']),\n", "            'Section_Type': element['css_context']['section_type'] or 'None',\n", "            'Component_Type': element['css_context']['component_type'] or 'None',\n", "            'Interactive': element['attributes_analysis']['interactive'],\n", "            'Media_Type': element['attributes_analysis']['media_type'] or 'None',\n", "            'Text_Content': element['text_content']\n", "        }\n", "        export_data.append(export_row)\n", "    \n", "    # Create DataFrame\n", "    df = pd.DataFrame(export_data)\n", "    \n", "    print(\"📊 TECHNICAL LABELS SUMMARY TABLE\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Display summary table\n", "    display_df = df[['Index', 'Technical_Label', 'HTML_Tag', 'Width', 'Height', 'Interactive']].copy()\n", "    print(display_df.to_string(index=False))\n", "    \n", "    # Export to JSON\n", "    technical_labels_json = {\n", "        'metadata': {\n", "            'total_elements': len(labeled_elements),\n", "            'generation_timestamp': pd.Timestamp.now().isoformat(),\n", "            'labeling_system': 'UI Element Technical Labeling System v1.0'\n", "        },\n", "        'elements': labeled_elements\n", "    }\n", "    \n", "    # Save to file\n", "    with open('technical_labels_output.json', 'w') as f:\n", "        json.dump(technical_labels_json, f, indent=2)\n", "    \n", "    # Save CSV\n", "    df.to_csv('technical_labels_summary.csv', index=False)\n", "    \n", "    print(f\"\\n✅ Results exported successfully!\")\n", "    print(f\"📄 JSON: technical_labels_output.json\")\n", "    print(f\"📊 CSV: technical_labels_summary.csv\")\n", "    \n", "    # Create mapping for easy reference\n", "    print(f\"\\n🗺️ QUICK REFERENCE MAPPING:\")\n", "    print(\"=\" * 40)\n", "    for element in labeled_elements:\n", "        print(f\"{element['index']:2d}. {element['original_label']} → {element['technical_label']}\")\n", "    \n", "else:\n", "    print(\"⚠️ No labeled elements available for export\")"]}, {"cell_type": "markdown", "metadata": {"id": "visualization"}, "source": ["## 📊 Visualization and Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualization_code"}, "outputs": [], "source": ["# Create visualizations if elements are available\n", "if 'labeled_elements' in locals() and labeled_elements:\n", "    \n", "    # Set up the plotting style\n", "    plt.style.use('default')\n", "    sns.set_palette(\"husl\")\n", "    \n", "    # Create subplots\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle('UI Element Technical Analysis Dashboard', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Element Types Distribution\n", "    html_tags = [element['html_tag'] for element in labeled_elements]\n", "    tag_counts = pd.Series(html_tags).value_counts()\n", "    \n", "    axes[0, 0].pie(tag_counts.values, labels=tag_counts.index, autopct='%1.1f%%', startangle=90)\n", "    axes[0, 0].set_title('HTML Tag Distribution')\n", "    \n", "    # 2. <PERSON><PERSON>\n", "    widths = [element['element_size']['width'] for element in labeled_elements]\n", "    heights = [element['element_size']['height'] for element in labeled_elements]\n", "    \n", "    scatter = axes[0, 1].scatter(widths, heights, alpha=0.7, s=100)\n", "    axes[0, 1].set_xlabel('Width (px)')\n", "    axes[0, 1].set_ylabel('Height (px)')\n", "    axes[0, 1].set_title('Element Dimensions')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # Add element labels to scatter plot\n", "    for i, element in enumerate(labeled_elements):\n", "        axes[0, 1].annotate(f\"{element['index']}\", \n", "                           (widths[i], heights[i]), \n", "                           xytext=(5, 5), textcoords='offset points', \n", "                           fontsize=8, alpha=0.8)\n", "    \n", "    # 3. Section Types\n", "    section_types = [element['css_context']['section_type'] or 'Unknown' for element in labeled_elements]\n", "    section_counts = pd.Series(section_types).value_counts()\n", "    \n", "    section_counts.plot(kind='bar', ax=axes[1, 0])\n", "    axes[1, 0].set_title('Section Type Distribution')\n", "    axes[1, 0].set_xlabel('Section Type')\n", "    axes[1, 0].set_ylabel('Count')\n", "    axes[1, 0].tick_params(axis='x', rotation=45)\n", "    \n", "    # 4. Interactive vs Non-Interactive\n", "    interactive_counts = pd.Series([element['attributes_analysis']['interactive'] for element in labeled_elements]).value_counts()\n", "    interactive_labels = ['Interactive' if x else 'Static' for x in interactive_counts.index]\n", "    \n", "    axes[1, 1].pie(interactive_counts.values, labels=interactive_labels, autopct='%1.1f%%', startangle=90)\n", "    axes[1, 1].set_title('Element Interactivity')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Statistics Summary\n", "    print(\"\\n📈 TECHNICAL LABELING STATISTICS\")\n", "    print(\"=\" * 50)\n", "    print(f\"📊 Total Elements Analyzed: {len(labeled_elements)}\")\n", "    print(f\"🏗️ Unique HTML Tags: {len(tag_counts)}\")\n", "    print(f\"🎯 Most Common Tag: {tag_counts.index[0]} ({tag_counts.iloc[0]} elements)\")\n", "    print(f\"🖱️ Interactive Elements: {sum(1 for e in labeled_elements if e['attributes_analysis']['interactive'])}\")\n", "    print(f\"📐 Average Element Size: {np.mean([e['element_size']['area'] for e in labeled_elements]):,.0f}px²\")\n", "    print(f\"🏢 Identified Sections: {len([s for s in section_types if s != 'Unknown'])}\")\n", "    \n", "    # Technical Label Complexity Analysis\n", "    label_lengths = [len(element['technical_label'].split()) for element in labeled_elements]\n", "    print(f\"🏷️ Average Label Complexity: {np.mean(label_lengths):.1f} words\")\n", "    print(f\"📝 Most Complex Label: {max(labeled_elements, key=lambda x: len(x['technical_label'].split()))['technical_label']}\")\n", "    \n", "else:\n", "    print(\"⚠️ No labeled elements available for visualization\")"]}, {"cell_type": "markdown", "metadata": {"id": "interactive_explorer"}, "source": ["## 🔍 Interactive Element Explorer"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "interactive_code"}, "outputs": [], "source": ["# Create interactive element explorer\n", "if 'labeled_elements' in locals() and labeled_elements:\n", "    \n", "    def display_element_details(element_index):\n", "        \"\"\"Display detailed information for a specific element\"\"\"\n", "        element = labeled_elements[element_index - 1]  # Convert to 0-based index\n", "        \n", "        print(f\"🏷️ ELEMENT {element['index']} DETAILED VIEW\")\n", "        print(\"=\" * 50)\n", "        print(f\"📍 Technical Label: {element['technical_label']}\")\n", "        print(f\"📋 Original Label: {element['original_label']}\")\n", "        print(f\"🏗️ HTML Tag: <{element['html_tag']}>\")\n", "        print(f\"📐 Dimensions: {element['element_size']['width']}×{element['element_size']['height']}px\")\n", "        print(f\"📍 Position: ({element['position']['x']}, {element['position']['y']})\")\n", "        \n", "        if element['css_classes']:\n", "            print(f\"🎨 CSS Classes: {', '.join(element['css_classes'])}\")\n", "        \n", "        if element['text_content']:\n", "            print(f\"📝 Text Content: {element['text_content']}\")\n", "        \n", "        print(f\"🖱️ Interactive: {'Yes' if element['attributes_analysis']['interactive'] else 'No'}\")\n", "        \n", "        if element['css_context']['section_type']:\n", "            print(f\"🏢 Section: {element['css_context']['section_type'].title()}\")\n", "    \n", "    # Create dropdown for element selection\n", "    element_options = [(f\"{e['index']}: {e['technical_label']}\", e['index']) for e in labeled_elements]\n", "    \n", "    element_dropdown = widgets.Dropdown(\n", "        options=element_options,\n", "        description='Select Element:',\n", "        style={'description_width': 'initial'},\n", "        layout=widgets.Layout(width='600px')\n", "    )\n", "    \n", "    output_area = widgets.Output()\n", "    \n", "    def on_element_change(change):\n", "        with output_area:\n", "            clear_output(wait=True)\n", "            display_element_details(change['new'])\n", "    \n", "    element_dropdown.observe(on_element_change, names='value')\n", "    \n", "    # Display initial element\n", "    with output_area:\n", "        display_element_details(1)\n", "    \n", "    display(widgets.VBox([\n", "        widgets.HTML(\"<h3>🔍 Interactive Element Explorer</h3>\"),\n", "        widgets.HTML(\"<p>Select an element from the dropdown to view its detailed technical information:</p>\"),\n", "        element_dropdown,\n", "        output_area\n", "    ]))\n", "    \n", "else:\n", "    print(\"⚠️ No labeled elements available for interactive exploration\")"]}, {"cell_type": "markdown", "metadata": {"id": "usage_guide"}, "source": ["## 📖 Usage Guide and Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "usage_guide_code"}, "outputs": [], "source": ["# Display usage guide and next steps\n", "print(\"📖 UI ELEMENT TECHNICAL LABELING SYSTEM - USAGE GUIDE\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n🎯 WHAT THIS SYSTEM DOES:\")\n", "print(\"• Analyzes UI elements from coordinates.json and element_info.json\")\n", "print(\"• Generates technical labels based on HTML structure, CSS classes, and attributes\")\n", "print(\"• Provides semantic understanding of element purpose and functionality\")\n", "print(\"• Maps elements to common landing page patterns and sections\")\n", "print(\"• Exports results in multiple formats (JSON, CSV)\")\n", "\n", "print(\"\\n🔧 HOW TO USE WITH YOUR OWN DATA:\")\n", "print(\"1. Prepare your coordinates.json file with element positions and basic labels\")\n", "print(\"2. Prepare your element_info.json file with comprehensive DOM data\")\n", "print(\"3. Update the file paths in the data_loader.load_files() call\")\n", "print(\"4. Run all cells to generate technical labels\")\n", "print(\"5. Use the interactive explorer to examine individual elements\")\n", "print(\"6. Export results using the provided export functions\")\n", "\n", "print(\"\\n📊 OUTPUT FILES GENERATED:\")\n", "print(\"• technical_labels_output.json - Complete technical analysis data\")\n", "print(\"• technical_labels_summary.csv - Tabular summary for spreadsheet analysis\")\n", "\n", "print(\"\\n🏷️ TECHNICAL LABEL FORMAT:\")\n", "print(\"[Section Type] [Component Type] [Semantic Description] [Media Type] (Interactive) [Style Modifiers]\")\n", "print(\"Example: 'Hero Video Element (Interactive) [Primary]'\")\n", "\n", "print(\"\\n🔍 ANALYSIS COMPONENTS:\")\n", "print(\"• HTML Tag Analysis - Semantic meaning from HTML elements\")\n", "print(\"• CSS Class Analysis - Context from class names and patterns\")\n", "print(\"• Attribute Analysis - Functionality from HTML attributes\")\n", "print(\"• Content Analysis - Purpose from text content\")\n", "print(\"• Position Analysis - Layout context from coordinates\")\n", "print(\"• Landing Page Mapping - Common web patterns recognition\")\n", "\n", "print(\"\\n🚀 NEXT STEPS:\")\n", "print(\"• Use technical labels for automated testing frameworks\")\n", "print(\"• Integrate with accessibility auditing tools\")\n", "print(\"• Apply to UI component documentation\")\n", "print(\"• Enhance with machine learning for pattern recognition\")\n", "print(\"• Extend labeling rules for specific domains or frameworks\")\n", "\n", "print(\"\\n✨ CUSTOMIZATION OPTIONS:\")\n", "print(\"• Modify landing_page_patterns in TechnicalLabeler class\")\n", "print(\"• Add new semantic_tags for additional HTML elements\")\n", "print(\"• Extend CSS context extraction rules\")\n", "print(\"• Customize label construction logic\")\n", "print(\"• Add domain-specific labeling patterns\")\n", "\n", "if 'labeled_elements' in locals() and labeled_elements:\n", "    print(f\"\\n🎊 CURRENT SESSION RESULTS:\")\n", "    print(f\"• Successfully processed {len(labeled_elements)} UI elements\")\n", "    print(f\"• Generated technical labels for all elements\")\n", "    print(f\"• Exported results to JSON and CSV formats\")\n", "    print(f\"• Created interactive element explorer\")\n", "    print(f\"• Generated visualization dashboard\")\n", "    \n", "    print(f\"\\n📈 QUALITY METRICS:\")\n", "    interactive_count = sum(1 for e in labeled_elements if e['attributes_analysis']['interactive'])\n", "    section_identified = sum(1 for e in labeled_elements if e['css_context']['section_type'])\n", "    print(f\"• Interactive elements identified: {interactive_count}/{len(labeled_elements)}\")\n", "    print(f\"• Section context identified: {section_identified}/{len(labeled_elements)}\")\n", "    print(f\"• Average label complexity: {np.mean([len(e['technical_label'].split()) for e in labeled_elements]):.1f} words\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🎯 Technical labeling system ready for production use!\")\n", "print(\"📧 For questions or enhancements, refer to the documentation.\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}