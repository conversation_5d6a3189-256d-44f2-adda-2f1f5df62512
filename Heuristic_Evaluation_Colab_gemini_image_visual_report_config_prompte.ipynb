# Install required packages for heuristic evaluation with image processing
!pip install -q google-generativeai
!pip install -q ipywidgets
!pip install -q pillow
!pip install -q matplotlib
!pip install -q opencv-python

print("✅ Packages for visual heuristic evaluation installed successfully!")

import os
from google.colab import userdata
import getpass

# Try to get API keys from Colab secrets first
try:
    GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')
    print("✅ Google API key loaded from Colab secrets")
except:
    print("⚠️ Google API key not found in secrets. Please enter manually:")
    GOOGLE_API_KEY = getpass.getpass("Enter your Google API Key: ")

# Set environment variable
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

print("🔑 API key configured successfully!")

import google.generativeai as genai

genai.configure(api_key=GOOGLE_API_KEY)

# List all available models
models = genai.list_models()

for model in models:
    print(model.name)


# Configuration class for Heuristic Evaluation
class Config:
    # API Keys
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")

    # Model configuration for heuristic evaluation with vision support
    MODEL_NAME = "gemini-2.5-flash-preview-05-20"  # Vision-capable model
    TEMPERATURE = 0.7
    MAX_TOKENS = 10000

    # Vision-specific configuration
    ENABLE_VISION = True
    IMAGE_QUALITY = "high"  # high, medium, low
    MAX_IMAGE_SIZE = (1024, 1024)  # Max dimensions for images sent to Gemini

    # Default file paths
    DEFAULT_COORDINATES_PATH = "coordinates.json"
    DEFAULT_ELEMENT_INFO_PATH = "element_info.json"
    DEFAULT_SCREENSHOT_PATH = "notioncom.png"

    # Heuristic evaluation configuration
    EXCLUDED_LABELS = ["Non-UI Element", "Unknown Element"]

    # Visual analysis configuration
    ELEMENT_HIGHLIGHT_COLOR = (255, 0, 0)  # Red for highlighting
    ELEMENT_BORDER_WIDTH = 3
    CROP_PADDING = 10  # Padding around cropped elements

    # ========================================
    # CUSTOMIZABLE EVALUATION INSTRUCTIONS
    # ========================================

    # Base evaluation instructions (used for all evaluations)
    BASE_EVALUATION_INSTRUCTIONS = """
You are a UX expert conducting a comprehensive heuristic evaluation of a UI element.

EVALUATION TASK:
Analyze this UI element against ALL 10 Nielsen's usability heuristics. For each heuristic, determine if there are any violations.

EVALUATION CRITERIA:
1. Identify specific violations with clear explanations
2. Provide actionable recommendations for improvements
3. Consider both functional and aesthetic aspects
4. Rate severity as: high, medium, or low
5. Focus on user experience impact
6. Consider accessibility and inclusive design
7. Evaluate consistency with design standards
8. Assess error prevention and recovery mechanisms

IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.
    """

    # Visual analysis instructions (used when images are provided)
    VISUAL_ANALYSIS_INSTRUCTIONS = """
📸 IMAGE ANALYSIS INSTRUCTIONS:
- The first image shows the full interface with the element highlighted in red
- The second image shows a close-up view of the specific element
- Analyze visual hierarchy, contrast, spacing, typography, colors, and overall design
- Consider how the element fits within the broader interface context
- Evaluate visual accessibility (contrast ratios, text size, etc.)
- Assess visual feedback and interaction affordances
- Look for visual inconsistencies or design pattern violations
- Consider mobile responsiveness and cross-device compatibility
- Evaluate visual clarity and information architecture
- Assess visual affordances and signifiers
    """

    # Visual context message (when images are available)
    VISUAL_CONTEXT_MESSAGE = """
🖼️ VISUAL CONTEXT: You have been provided with actual screenshots showing this UI element. Use these images to conduct a comprehensive visual analysis alongside the technical data.
    """

    # No visual context message (when images are not available)
    NO_VISUAL_CONTEXT_MESSAGE = """
📝 VISUAL CONTEXT: No images provided - evaluate based on technical data and element properties only.
    """

    # Section-wise evaluation instructions
    SECTION_EVALUATION_INSTRUCTIONS = """
SECTION-WISE EVALUATION TASK:
Analyze this UI section against ALL 10 Nielsen's usability heuristics, considering:
1. How child elements work together as a cohesive unit
2. Information architecture and content organization
3. Navigation flow and user journey within the section
4. Visual hierarchy and grouping of related elements
5. Consistency of interaction patterns within the section
6. Overall section usability and user experience
7. Accessibility of the section as a whole
8. Error handling and feedback mechanisms
    """

    # Level 0 (Section) evaluation instructions
    LEVEL0_EVALUATION_INSTRUCTIONS = """
TWO-LEVEL EVALUATION CONTEXT:
• Level 0 (Current): Evaluate the SECTION as a whole organizational unit
• Level 1 (Children): Immediate children will be evaluated separately

LEVEL 0 SECTION EVALUATION FOCUS:
Evaluate this section as an ORGANIZATIONAL CONTAINER, focusing on:
1. Section-level information architecture and content grouping
2. Overall visual hierarchy and layout organization
3. Section navigation and wayfinding
4. Consistency of section-wide interaction patterns
5. Section accessibility and inclusive design
6. Error prevention at the section level
7. Section-wide feedback and status communication
8. Integration with broader application context
    """

    # Level 1 (Child) evaluation instructions
    LEVEL1_EVALUATION_INSTRUCTIONS = """
TWO-LEVEL EVALUATION CONTEXT:
• Level 0 (Parent): Section has been evaluated separately as organizational unit
• Level 1 (Current): Evaluate this IMMEDIATE CHILD individually
• Level 2+ (Context): Nested children provide context but are NOT evaluated individually

LEVEL 1 IMMEDIATE CHILD EVALUATION FOCUS:
Evaluate this immediate child as an INDIVIDUAL FUNCTIONAL ELEMENT, considering:
1. Individual element usability and interaction design
2. Element-specific accessibility and inclusive design
3. Individual element feedback and status indication
4. Element consistency within its parent section context
5. Individual element error prevention and recovery
6. Element-specific visual design and affordances
7. Individual element performance and efficiency
8. Element integration with sibling elements
    """

config = Config()
print("✅ Heuristic Evaluation Configuration loaded successfully!")
print("🔧 Customizable evaluation instructions available in config")
print("📋 Available instruction types:")
print("   • BASE_EVALUATION_INSTRUCTIONS")
print("   • VISUAL_ANALYSIS_INSTRUCTIONS")
print("   • VISUAL_CONTEXT_MESSAGE")
print("   • NO_VISUAL_CONTEXT_MESSAGE")
print("   • SECTION_EVALUATION_INSTRUCTIONS")
print("   • LEVEL0_EVALUATION_INSTRUCTIONS")
print("   • LEVEL1_EVALUATION_INSTRUCTIONS")

import json
import re
import os
import base64
import io
import google.generativeai as genai
from typing import Dict, List, Any, Optional, Tuple
import ipywidgets as widgets
from IPython.display import display, HTML, clear_output, Image as IPImage
from google.colab import files
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

print("✅ Libraries for visual heuristic evaluation imported successfully!")

class VisualAnalyzer:
    """Utilities for visual analysis of UI elements using screenshots"""

    def __init__(self):
        self.config = config
        self.screenshot = None
        self.screenshot_path = None

    def load_screenshot(self, screenshot_path: str = None) -> bool:
        """Load screenshot image for visual analysis"""
        try:
            if screenshot_path is None:
                screenshot_path = self.config.DEFAULT_SCREENSHOT_PATH

            if os.path.exists(screenshot_path):
                self.screenshot = Image.open(screenshot_path)
                self.screenshot_path = screenshot_path
                print(f"✅ Screenshot loaded: {screenshot_path} ({self.screenshot.size})")
                return True
            else:
                print(f"⚠️ Screenshot not found: {screenshot_path}")
                return False

        except Exception as e:
            print(f"❌ Error loading screenshot: {str(e)}")
            return False

    def crop_element(self, coordinates: Dict[str, int], padding: int = None) -> Image.Image:
        """Crop element from screenshot based on coordinates"""
        if self.screenshot is None:
            raise ValueError("No screenshot loaded. Call load_screenshot() first.")

        if padding is None:
            padding = self.config.CROP_PADDING

        # Extract coordinates
        x = coordinates.get('x', 0)
        y = coordinates.get('y', 0)
        width = coordinates.get('width', 100)
        height = coordinates.get('height', 100)

        # Calculate crop box with padding
        left = max(0, x - padding)
        top = max(0, y - padding)
        right = min(self.screenshot.width, x + width + padding)
        bottom = min(self.screenshot.height, y + height + padding)

        # Crop the element
        cropped = self.screenshot.crop((left, top, right, bottom))

        return cropped

    def highlight_element(self, coordinates: Dict[str, int], label: str = "") -> Image.Image:
        """Create highlighted version of screenshot with element outlined"""
        if self.screenshot is None:
            raise ValueError("No screenshot loaded. Call load_screenshot() first.")

        # Create a copy of the screenshot
        highlighted = self.screenshot.copy()
        draw = ImageDraw.Draw(highlighted)

        # Extract coordinates
        x = coordinates.get('x', 0)
        y = coordinates.get('y', 0)
        width = coordinates.get('width', 100)
        height = coordinates.get('height', 100)

        # Draw rectangle around element
        rectangle = [(x, y), (x + width, y + height)]
        draw.rectangle(
            rectangle,
            outline=self.config.ELEMENT_HIGHLIGHT_COLOR,
            width=self.config.ELEMENT_BORDER_WIDTH
        )

        # Add label if provided
        if label:
            try:
                # Try to use a default font
                font = ImageFont.load_default()
                text_bbox = draw.textbbox((0, 0), label, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

                # Position label above the element
                label_x = x
                label_y = max(0, y - text_height - 5)

                # Draw background for text
                draw.rectangle(
                    [(label_x, label_y), (label_x + text_width + 4, label_y + text_height + 4)],
                    fill=(255, 255, 255, 200)
                )

                # Draw text
                draw.text((label_x + 2, label_y + 2), label, fill=(0, 0, 0), font=font)

            except Exception as e:
                print(f"⚠️ Could not add label: {str(e)}")

        return highlighted

    def create_element_visualization(self, coordinates: Dict[str, int], label: str = "") -> Tuple[Image.Image, Image.Image]:
        """Create both highlighted screenshot and cropped element image"""
        highlighted = self.highlight_element(coordinates, label)
        cropped = self.crop_element(coordinates)

        return highlighted, cropped

    def display_element_analysis(self, coordinates: Dict[str, int], label: str = ""):
        """Display visual analysis of an element"""
        try:
            highlighted, cropped = self.create_element_visualization(coordinates, label)

            # Create subplot for side-by-side display
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # Display highlighted screenshot
            ax1.imshow(highlighted)
            ax1.set_title(f"Full Screenshot - {label}" if label else "Full Screenshot")
            ax1.axis('off')

            # Display cropped element
            ax2.imshow(cropped)
            ax2.set_title(f"Element Close-up - {label}" if label else "Element Close-up")
            ax2.axis('off')

            plt.tight_layout()
            plt.show()

            return highlighted, cropped

        except Exception as e:
            print(f"❌ Error creating visual analysis: {str(e)}")
            return None, None

    def encode_image_for_gemini(self, image: Image.Image) -> str:
        """Encode image for Gemini Vision API"""
        try:
            # Resize image if it's too large
            if image.size[0] > self.config.MAX_IMAGE_SIZE[0] or image.size[1] > self.config.MAX_IMAGE_SIZE[1]:
                image = image.copy()
                image.thumbnail(self.config.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)
                print(f"📏 Image resized to {image.size} for Gemini Vision API")

            # Convert PIL Image to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()

            # Encode to base64
            img_base64 = base64.b64encode(img_byte_arr).decode('utf-8')

            return img_base64

        except Exception as e:
            print(f"❌ Error encoding image: {str(e)}")
            return None

    def prepare_images_for_evaluation(self, coordinates: Dict[str, int]) -> Dict[str, Any]:
        """Prepare both full screenshot and cropped element images for Gemini Vision API"""
        if self.screenshot is None:
            return {"has_images": False, "error": "No screenshot loaded"}

        try:
            # Create highlighted screenshot and cropped element
            highlighted, cropped = self.create_element_visualization(coordinates)

            # Prepare images for Gemini
            images_data = {
                "has_images": True,
                "full_screenshot": {
                    "image": highlighted,
                    "description": "Full screenshot with highlighted element"
                },
                "element_closeup": {
                    "image": cropped,
                    "description": "Close-up view of the specific element"
                }
            }

            return images_data

        except Exception as e:
            print(f"❌ Error preparing images for evaluation: {str(e)}")
            return {"has_images": False, "error": str(e)}

print("✅ VisualAnalyzer class defined!")

class HeuristicEvaluator:
    """
    Gemini-Powered Heuristic Evaluator for UI Elements

    Uses Gemini AI model to evaluate each UI element individually against established usability principles:
    - Visibility of system status
    - Match between system and real world
    - User control and freedom
    - Consistency and standards
    - Error prevention
    - Recognition rather than recall
    - Flexibility and efficiency of use
    - Aesthetic and minimalist design
    - Help users recognize, diagnose, and recover from errors
    - Help and documentation
    """

    def __init__(self):
        self.config = config

        # Initialize Gemini model
        genai.configure(api_key=self.config.GOOGLE_API_KEY)
        self.model = genai.GenerativeModel(self.config.MODEL_NAME)

        # Define heuristic principles for Gemini evaluation
        self.heuristic_principles = self._get_heuristic_principles()

        print("✅ HeuristicEvaluator initialized with Gemini AI!")

    def _get_heuristic_principles(self) -> str:
        """Get comprehensive heuristic evaluation principles for Gemini"""
        return """
        NIELSEN'S 10 USABILITY HEURISTICS FOR UI EVALUATION:

        1. VISIBILITY OF SYSTEM STATUS
        - The system should always keep users informed about what is going on
        - Provide appropriate feedback within reasonable time
        - Show loading states, progress indicators, current page/state
        - Interactive elements should provide visual feedback (hover, focus, active states)

        2. MATCH BETWEEN SYSTEM AND REAL WORLD
        - The system should speak the users' language
        - Use words, phrases and concepts familiar to the user
        - Follow real-world conventions
        - Make information appear in natural and logical order

        3. USER CONTROL AND FREEDOM
        - Users often choose system functions by mistake
        - Provide clearly marked "emergency exit" to leave unwanted state
        - Support undo and redo
        - Give users control over their experience

        4. CONSISTENCY AND STANDARDS
        - Users should not have to wonder whether different words, situations, or actions mean the same thing
        - Follow platform conventions and established design patterns
        - Maintain internal consistency throughout the interface

        5. ERROR PREVENTION
        - Even better than good error messages is a careful design that prevents problems from occurring
        - Eliminate error-prone conditions
        - Present users with confirmation options before committing to important actions

        6. RECOGNITION RATHER THAN RECALL
        - Minimize the user's memory load
        - Make objects, actions, and options visible
        - User should not have to remember information from one part of the dialogue to another
        - Instructions for use should be visible or easily retrievable

        7. FLEXIBILITY AND EFFICIENCY OF USE
        - Accelerators may speed up interaction for expert users
        - Allow users to tailor frequent actions
        - Provide shortcuts and customization options

        8. AESTHETIC AND MINIMALIST DESIGN
        - Dialogues should not contain information that is irrelevant or rarely needed
        - Every extra unit of information competes with relevant units of information
        - Keep interfaces clean and focused

        9. HELP USERS RECOGNIZE, DIAGNOSE, AND RECOVER FROM ERRORS
        - Error messages should be expressed in plain language
        - Precisely indicate the problem
        - Constructively suggest a solution

        10. HELP AND DOCUMENTATION
        - Even though it's better if the system can be used without documentation
        - Provide help and documentation when needed
        - Information should be easy to search and focused on user's task
        """

print("✅ HeuristicEvaluator class defined!")

# Enhance HeuristicEvaluator with visual analysis capabilities
def enhance_with_visual_analysis(self):
    """Add visual analyzer to existing HeuristicEvaluator instance"""
    if not hasattr(self, 'visual_analyzer'):
        self.visual_analyzer = VisualAnalyzer()
        print("✅ Visual analyzer added to HeuristicEvaluator!")
    else:
        print("✅ Visual analyzer already available!")

def load_screenshot_for_evaluation(self, screenshot_path: str = None) -> bool:
    """Load screenshot for visual-enhanced heuristic evaluation"""
    if not hasattr(self, 'visual_analyzer'):
        self.enhance_with_visual_analysis()

    return self.visual_analyzer.load_screenshot(screenshot_path)

def configure_vision(self, enable_vision: bool = True, image_quality: str = "high", max_image_size: tuple = (1024, 1024)):
    """Configure vision settings for Gemini evaluation"""
    self.config.ENABLE_VISION = enable_vision
    self.config.IMAGE_QUALITY = image_quality
    self.config.MAX_IMAGE_SIZE = max_image_size

    status = "enabled" if enable_vision else "disabled"
    print(f"🔧 Vision analysis {status} for Gemini evaluations")
    if enable_vision:
        print(f"   📏 Max image size: {max_image_size}")
        print(f"   🎨 Image quality: {image_quality}")
        print(f"   🤖 Model: {self.config.MODEL_NAME} (vision-capable)")

    return enable_vision

def evaluate_element_with_visual(self, element_data: Dict[str, Any], coordinate_data: Dict[str, Any], show_visual: bool = True) -> Dict[str, Any]:
    """Enhanced evaluation with visual context from screenshot - NOW WITH ACTUAL IMAGE PASSING TO GEMINI"""

    # Prepare element information for Gemini
    element_info = {
        "index": coordinate_data.get("index", -1),
        "label": coordinate_data.get("label", "Unknown"),
        "coordinates": coordinate_data.get("coordinates", {}),
        "tag": element_data.get("tag", "unknown"),
        "text": element_data.get("text", ""),
        "css_selector": element_data.get("cssSelector", ""),
        "xpath": element_data.get("xpath", ""),
        "computed_style": element_data.get("computedStyle", {}),
        "attributes": element_data.get("attributes", {})
    }

    # Show visual analysis if requested and screenshot is available
    if show_visual and hasattr(self, 'visual_analyzer') and self.visual_analyzer.screenshot is not None:
        try:
            print(f"\n🖼️ Visual Analysis for {element_info['label']}:")
            highlighted, cropped = self.visual_analyzer.display_element_analysis(
                element_info['coordinates'],
                element_info['label']
            )
        except Exception as e:
            print(f"⚠️ Could not display visual analysis: {str(e)}")

    try:
        # Check if vision is enabled and visual analyzer is available
        has_visual_context = (hasattr(self, 'visual_analyzer') and
                            self.visual_analyzer.screenshot is not None and
                            self.config.ENABLE_VISION)

        if has_visual_context:
            print(f"🔍 Preparing images for Gemini Vision API evaluation...")

            # Prepare images for Gemini Vision API
            images_data = self.visual_analyzer.prepare_images_for_evaluation(element_info['coordinates'])

            if images_data.get('has_images', False):
                # Create multimodal content with both text and images
                content_parts = []

                # Add text prompt
                prompt_text = self._create_visual_evaluation_prompt(element_info, with_images=True)
                content_parts.append(prompt_text)

                # Add full screenshot with highlighted element
                content_parts.append(images_data['full_screenshot']['image'])
                content_parts.append("📸 FULL SCREENSHOT: This shows the complete interface with the element highlighted in red.")

                # Add element close-up
                content_parts.append(images_data['element_closeup']['image'])
                content_parts.append("🔍 ELEMENT CLOSE-UP: This shows a detailed view of the specific element being evaluated.")

                print(f"📤 Sending multimodal content to Gemini (text + 2 images)...")

                # Send multimodal content to Gemini
                response = self.model.generate_content(content_parts)

                print(f"✅ Received vision-enhanced evaluation from Gemini!")

            else:
                print(f"⚠️ Could not prepare images: {images_data.get('error', 'Unknown error')}")
                # Fallback to text-only evaluation
                prompt = self._create_visual_evaluation_prompt(element_info, with_images=False)
                response = self.model.generate_content(prompt)
        else:
            print(f"📝 Using text-only evaluation (vision disabled or no screenshot)")
            # Text-only evaluation
            prompt = self._create_visual_evaluation_prompt(element_info, with_images=False)
            response = self.model.generate_content(prompt)

        # Parse Gemini response into structured format
        evaluation_result = self._parse_gemini_response(response.text, element_info)

        # Add visual analysis flags
        evaluation_result['has_visual_context'] = has_visual_context
        evaluation_result['vision_enabled'] = self.config.ENABLE_VISION
        evaluation_result['images_sent_to_gemini'] = has_visual_context and images_data.get('has_images', False)

        return evaluation_result

    except Exception as e:
        print(f"❌ Error in visual evaluation: {str(e)}")
        # Fallback evaluation result in case of error
        return {
            "element_info": element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in visual evaluation: {str(e)}",
            "evaluation_status": "error",
            "has_visual_context": False,
            "vision_enabled": self.config.ENABLE_VISION,
            "images_sent_to_gemini": False
        }

def _create_visual_evaluation_prompt(self, element_info: Dict[str, Any], with_images: bool = False) -> str:
    """Create enhanced evaluation prompt with visual context awareness"""

    element_json = json.dumps(element_info, indent=2)

    # Determine visual context based on whether images are being sent
    if with_images:
        visual_context = self.config.VISUAL_CONTEXT_MESSAGE
        image_instructions = self.config.VISUAL_ANALYSIS_INSTRUCTIONS
    else:
        visual_context = self.config.NO_VISUAL_CONTEXT_MESSAGE
        image_instructions = ""

    prompt = f"""
You are a UX expert conducting a comprehensive heuristic evaluation of a UI element with enhanced visual analysis capabilities.
{visual_context}{image_instructions}

HEURISTIC EVALUATION PRINCIPLES:
{self.heuristic_principles}

ELEMENT TO EVALUATE:
{element_json}

ENHANCED EVALUATION TASK:
Analyze this UI element against ALL 10 Nielsen's usability heuristics. {'Use the provided images to conduct detailed visual analysis alongside the technical data.' if with_images else 'Focus on technical data and inferred visual properties.'}

VISUAL EVALUATION FOCUS:
1. **Visual Hierarchy**: How well does the element stand out or blend appropriately?
2. **Visual Feedback**: Does the element provide clear visual states and feedback?
3. **Aesthetic Design**: Is the element visually appealing and consistent?
4. **Spatial Relationships**: How does the element relate to surrounding elements?
5. **Accessibility**: Is the element visually accessible (contrast, size, etc.)?
6. **User Expectations**: Does the visual design match user expectations?
7. **Context Integration**: How well does the element integrate with the overall interface?
8. **Visual Affordances**: Are interaction possibilities clearly communicated visually?

RESPONSE FORMAT (JSON):
{{
    "violations": [
        {{
            "heuristic": "Heuristic Name",
            "violation": "Brief description of violation",
            "reason": "Detailed explanation including visual aspects",
            "severity": "high|medium|low",
            "recommendation": "Specific actionable recommendation including visual improvements",
            "visual_impact": "How visual design contributes to this violation"
        }}
    ],
    "passed_checks": [
        "List of heuristic names that this element passes"
    ],
    "overall_score": 85,
    "summary": "Brief overall assessment including visual usability",
    "key_recommendations": [
        "Most important recommendations including visual improvements"
    ],
    "visual_assessment": "Specific evaluation of visual design aspects{'based on the provided screenshots' if with_images else 'based on technical properties'}",
    "image_analysis": "{'Detailed analysis of what you observe in the provided images' if with_images else 'No images provided for analysis'}"
}}

EVALUATION GUIDELINES:
1. Be thorough - check ALL 10 heuristics {'with visual context from images' if with_images else 'based on technical data'}
2. {'Analyze the actual visual appearance, colors, spacing, and layout from the images' if with_images else 'Infer visual properties from CSS and element attributes'}
3. Evaluate visual hierarchy, contrast, and accessibility
4. Assess visual feedback and interaction states
5. Consider visual consistency with design patterns
6. Provide specific visual design recommendations
7. Rate severity based on both functional and visual impact
8. {'Use the images to identify visual issues not apparent from technical data alone' if with_images else 'Focus on technical compliance and inferred visual properties'}

IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.
"""
    return prompt

# Bind enhanced methods to the HeuristicEvaluator class
HeuristicEvaluator.enhance_with_visual_analysis = enhance_with_visual_analysis
HeuristicEvaluator.load_screenshot_for_evaluation = load_screenshot_for_evaluation
HeuristicEvaluator.configure_vision = configure_vision
HeuristicEvaluator.evaluate_element_with_visual = evaluate_element_with_visual
HeuristicEvaluator._create_visual_evaluation_prompt = _create_visual_evaluation_prompt

print("✅ HeuristicEvaluator enhanced with VISION-ENABLED visual analysis capabilities!")
print("🖼️ Images will now be sent to Gemini model for comprehensive visual evaluation!")
print("\n📋 NEW FEATURES:")
print("   🔧 configure_vision() - Enable/disable vision and configure image settings")
print("   📸 Images automatically sent to Gemini during evaluation")
print("   🎯 Enhanced prompts for multimodal analysis")
print("   📊 Vision status tracking in evaluation results")

# Add evaluation methods to HeuristicEvaluator class
def evaluate_element(self, element_data: Dict[str, Any], coordinate_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Evaluate a single UI element using Gemini AI against all heuristics

    Args:
        element_data: Complete DOM element information
        coordinate_data: Coordinate and label information

    Returns:
        Dictionary containing evaluation results from Gemini AI
    """
    # Prepare element information for Gemini
    element_info = {
        "index": coordinate_data.get("index", -1),
        "label": coordinate_data.get("label", "Unknown"),
        "coordinates": coordinate_data.get("coordinates", {}),
        "tag": element_data.get("tag", "unknown"),
        "text": element_data.get("text", ""),
        "css_selector": element_data.get("cssSelector", ""),
        "xpath": element_data.get("xpath", ""),
        "computed_style": element_data.get("computedStyle", {}),
        "attributes": element_data.get("attributes", {})
    }

    # Create comprehensive prompt for Gemini evaluation
    prompt = self._create_evaluation_prompt(element_info)

    try:
        # Get Gemini evaluation
        response = self.model.generate_content(prompt)

        # Parse Gemini response into structured format
        evaluation_result = self._parse_gemini_response(response.text, element_info)

        return evaluation_result

    except Exception as e:
        # Fallback evaluation result in case of error
        return {
            "element_info": element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in Gemini evaluation: {str(e)}",
            "evaluation_status": "error"
        }

def should_evaluate_element(self, coordinate_data: Dict[str, Any]) -> bool:
    """
    Determine if an element should be evaluated based on its label

    Args:
        coordinate_data: Coordinate and label information

    Returns:
        Boolean indicating whether to evaluate the element
    """
    label = coordinate_data.get("label", "")
    return label not in self.config.EXCLUDED_LABELS

# Bind methods to the HeuristicEvaluator class
HeuristicEvaluator.evaluate_element = evaluate_element
HeuristicEvaluator.should_evaluate_element = should_evaluate_element

print("✅ HeuristicEvaluator evaluation methods added!")

# Add prompt creation and response parsing methods to HeuristicEvaluator class
def _create_evaluation_prompt(self, element_info: Dict[str, Any]) -> str:
    """Create comprehensive evaluation prompt for Gemini"""

    element_json = json.dumps(element_info, indent=2)

    prompt = f"""
{self.config.BASE_EVALUATION_INSTRUCTIONS}

HEURISTIC EVALUATION PRINCIPLES:
{self.heuristic_principles}

ELEMENT TO EVALUATE:
{element_json}

RESPONSE FORMAT (JSON):
{{
    "violations": [
        {{
            "heuristic": "Heuristic Name",
            "violation": "Brief description of violation",
            "reason": "Detailed explanation of why this is a violation",
            "severity": "high|medium|low",
            "recommendation": "Specific actionable recommendation to fix this issue"
        }}
    ],
    "passed_checks": [
        "List of heuristic names that this element passes"
    ],
    "overall_score": 85,
    "summary": "Brief overall assessment of the element's usability",
    "key_recommendations": [
        "Most important recommendations for improvement"
    ]
}}

EVALUATION GUIDELINES:
1. Be thorough - check ALL 10 heuristics
2. Consider the element's context, purpose, and user expectations
3. Look at technical aspects: size, positioning, styling, text content
4. Consider accessibility and usability best practices
5. Provide specific, actionable recommendations
6. Rate severity based on impact on user experience
7. If no violations found for a heuristic, add it to passed_checks

IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.
"""
    return prompt

def _parse_gemini_response(self, response_text: str, element_info: Dict[str, Any]) -> Dict[str, Any]:
    """Parse Gemini's JSON response into structured evaluation result"""
    try:
        # Try to extract JSON from response
        response_text = response_text.strip()

        # Remove any markdown code blocks if present
        if response_text.startswith("```json"):
            response_text = response_text[7:]
        if response_text.startswith("```"):
            response_text = response_text[3:]
        if response_text.endswith("```"):
            response_text = response_text[:-3]

        # Parse JSON response
        gemini_result = json.loads(response_text.strip())

        # Structure the result according to our format
        evaluation_result = {
            "element_info": element_info,
            "violations": gemini_result.get("violations", []),
            "passed_checks": gemini_result.get("passed_checks", []),
            "overall_score": gemini_result.get("overall_score", 0),
            "recommendations": gemini_result.get("key_recommendations", []),
            "gemini_analysis": gemini_result.get("summary", ""),
            "evaluation_status": "success"
        }

        return evaluation_result

    except json.JSONDecodeError as e:
        # Fallback: try to extract information from text response
        return self._parse_text_response(response_text, element_info)
    except Exception as e:
        # Error fallback
        return {
            "element_info": element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error parsing Gemini response: {str(e)}",
            "evaluation_status": "parse_error"
        }

# Bind methods to the HeuristicEvaluator class
HeuristicEvaluator._create_evaluation_prompt = _create_evaluation_prompt
HeuristicEvaluator._parse_gemini_response = _parse_gemini_response

print("✅ HeuristicEvaluator prompt and parsing methods added!")

# Add fallback text parsing method to HeuristicEvaluator class
def _parse_text_response(self, response_text: str, element_info: Dict[str, Any]) -> Dict[str, Any]:
    """Fallback parser for non-JSON responses from Gemini"""
    try:
        # Try to extract key information from text response
        violations = []
        passed_checks = []
        recommendations = []

        # Look for violation patterns in text
        lines = response_text.split('\n')
        current_violation = {}

        for line in lines:
            line = line.strip()
            if 'violation' in line.lower() or 'issue' in line.lower():
                if current_violation:
                    violations.append(current_violation)
                current_violation = {
                    "heuristic": "General Usability",
                    "violation": line,
                    "reason": "Identified by Gemini analysis",
                    "severity": "medium"
                }
            elif 'recommendation' in line.lower() or 'suggest' in line.lower():
                recommendations.append(line)

        if current_violation:
            violations.append(current_violation)

        # Estimate score based on violations found
        score = max(0, 100 - (len(violations) * 15))

        return {
            "element_info": element_info,
            "violations": violations,
            "passed_checks": passed_checks,
            "overall_score": score,
            "recommendations": recommendations,
            "gemini_analysis": response_text,
            "evaluation_status": "text_parsed"
        }

    except Exception as e:
        return {
            "element_info": element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in text parsing: {str(e)}",
            "evaluation_status": "text_parse_error"
        }

# Bind method to the HeuristicEvaluator class
HeuristicEvaluator._parse_text_response = _parse_text_response

print("✅ HeuristicEvaluator text parsing method added!")

# Data loading functions for heuristic evaluation
def load_data_from_config():
    """Load coordinates and element info data from configuration file paths"""

    print("📁 Loading data for heuristic evaluation...")
    print(f"📍 Coordinates file: {config.DEFAULT_COORDINATES_PATH}")
    print(f"🏗️ Element info file: {config.DEFAULT_ELEMENT_INFO_PATH}")

    coordinates_data = None
    element_info_data = None

    try:
        # Try to load coordinates.json
        try:
            with open(config.DEFAULT_COORDINATES_PATH, 'r', encoding='utf-8') as f:
                coordinates_data = json.load(f)
            print(f"✅ Loaded coordinates data: {len(coordinates_data)} elements")

            # Filter out excluded elements for heuristic evaluation
            filtered_coords = []
            for i, coord in enumerate(coordinates_data):
                if coord.get('label', '') not in config.EXCLUDED_LABELS:
                    coord['index'] = i  # Add index for reference
                    filtered_coords.append(coord)

            print(f"📊 Elements for evaluation: {len(filtered_coords)} (excluded {len(coordinates_data) - len(filtered_coords)} non-UI elements)")
            coordinates_data = filtered_coords

        except FileNotFoundError:
            print(f"⚠️ {config.DEFAULT_COORDINATES_PATH} not found. You can upload your own file below.")
        except Exception as e:
            print(f"❌ Error loading coordinates: {e}")

        # Try to load element_info.json
        try:
            with open(config.DEFAULT_ELEMENT_INFO_PATH, 'r', encoding='utf-8') as f:
                element_info_data = json.load(f)
            print(f"✅ Loaded element info data: {len(element_info_data)} elements")
        except FileNotFoundError:
            print(f"⚠️ {config.DEFAULT_ELEMENT_INFO_PATH} not found. You can upload your own file below.")
        except Exception as e:
            print(f"❌ Error loading element info: {e}")

        return coordinates_data, element_info_data

    except Exception as e:
        print(f"❌ Error in data loading: {e}")
        return None, None

def create_fallback_data():
    """Create minimal fallback data for demonstration when files are not available"""
    print("\n🔄 Creating fallback demonstration data...")

    fallback_coordinates = [
        {
            "coordinates": {"x": 949, "y": 385, "width": 626, "height": 330},
            "label": "Video",
            "index": 0
        },
        {
            "coordinates": {"x": 323, "y": 451, "width": 602, "height": 128},
            "label": "Main Heading",
            "index": 1
        },
        {
            "coordinates": {"x": 725, "y": 2666, "width": 447, "height": 90},
            "label": "Button",
            "index": 2
        }
    ]

    fallback_element_info = {
        "element_1": {
            "tag": "video",
            "text": "",
            "cssSelector": "video.Video_video__KYz0l",
            "xpath": "//*[@id='__next']/div[1]/video[1]",
            "src": "https://example.com/video.mp4",
            "classes": ["Video_video__KYz0l"],
            "attributes": {"autoplay": "", "muted": ""},
            "computedStyle": {"display": "block", "width": "626px"}
        },
        "element_2": {
            "tag": "h1",
            "text": "Sample Heading Text",
            "cssSelector": "h1.heading",
            "xpath": "//*[@id='__next']/h1[1]",
            "classes": ["heading"],
            "attributes": {"class": "heading"},
            "computedStyle": {"font-size": "64px", "color": "rgb(25, 25, 24)"}
        },
        "element_3": {
            "tag": "button",
            "text": "Click Me",
            "cssSelector": "button.primary",
            "xpath": "//*[@id='content']/button[1]",
            "classes": ["primary", "btn"],
            "attributes": {"class": "primary btn", "type": "button"},
            "computedStyle": {"background-color": "rgb(0, 123, 255)", "color": "white"}
        }
    }

    print(f"📊 Fallback coordinates: {len(fallback_coordinates)} elements")
    print(f"🏗️ Fallback element info: {len(fallback_element_info)} elements")

    return fallback_coordinates, fallback_element_info

print("✅ Data loading functions defined!")

# Enhanced hierarchical data processing functions for section-wise evaluation
def load_hierarchical_data_from_config():
    """Load coordinates and element info data with hierarchical structure support"""

    print("📁 Loading hierarchical data for section-wise heuristic evaluation...")
    print(f"📍 Coordinates file: {config.DEFAULT_COORDINATES_PATH}")
    print(f"🏗️ Element info file: {config.DEFAULT_ELEMENT_INFO_PATH}")

    coordinates_data = None
    element_info_data = None

    try:
        # Try to load coordinates.json (hierarchical structure)
        try:
            with open(config.DEFAULT_COORDINATES_PATH, 'r', encoding='utf-8') as f:
                coordinates_data = json.load(f)
            print(f"✅ Loaded hierarchical coordinates data: {len(coordinates_data)} root elements")

        except FileNotFoundError:
            print(f"⚠️ {config.DEFAULT_COORDINATES_PATH} not found. You can upload your own file below.")
        except Exception as e:
            print(f"❌ Error loading coordinates: {e}")

        # Try to load element_info.json (with children data)
        try:
            with open(config.DEFAULT_ELEMENT_INFO_PATH, 'r', encoding='utf-8') as f:
                element_info_data = json.load(f)
            print(f"✅ Loaded hierarchical element info data: {len(element_info_data)} elements")
        except FileNotFoundError:
            print(f"⚠️ {config.DEFAULT_ELEMENT_INFO_PATH} not found. You can upload your own file below.")
        except Exception as e:
            print(f"❌ Error loading element info: {e}")

        return coordinates_data, element_info_data

    except Exception as e:
        print(f"❌ Error in data loading: {e}")
        return None, None

def extract_sections_and_children(coordinates_data):
    """Extract sections and their child elements from hierarchical data"""

    sections = []
    all_elements = []

    def process_element(element_key, element_data, parent_section=None, depth=0):
        """Recursively process elements and identify sections"""

        element_info = {
            'key': element_key,
            'data': element_data,
            'parent_section': parent_section,
            'depth': depth,
            'is_section': 'Section' in element_data.get('label', ''),
            'children': []
        }

        # If this is a section, track it
        if element_info['is_section']:
            sections.append(element_info)
            current_section = element_info
        else:
            current_section = parent_section

        # Process children recursively
        children_data = element_data.get('children', [])
        for i, child in enumerate(children_data):
            child_key = f"{element_key}_child_{i}"
            child_info = process_element(child_key, child, current_section, depth + 1)
            element_info['children'].append(child_info)

        all_elements.append(element_info)
        return element_info

    # Process all root elements
    for element_key, element_data in coordinates_data.items():
        process_element(element_key, element_data)

    print(f"📊 Extracted {len(sections)} sections with hierarchical structure")
    print(f"🏗️ Total elements processed: {len(all_elements)}")

    return sections, all_elements

def get_all_child_elements(element_info, include_excluded=False):
    """Get all child elements from a section, optionally including excluded elements"""

    child_elements = []

    def collect_children(element):
        """Recursively collect all child elements"""
        for child in element.get('children', []):
            # Check if element should be included
            child_label = child.get('data', {}).get('label', '')

            if include_excluded or child_label not in config.EXCLUDED_LABELS:
                child_elements.append(child)

            # Recursively collect from this child's children
            collect_children(child)

    collect_children(element_info)
    return child_elements

print("✅ Enhanced hierarchical data processing functions defined!")

# Two-Level Evaluation System Functions
def extract_sections_with_two_levels(coordinates_data):
    """Extract sections with two-level structure: Level 0 (sections) and Level 1 (immediate children)"""

    sections = []
    all_elements = []

    def process_element(element_key, element_data, parent_section=None, depth=0):
        """Recursively process elements and identify sections with two-level structure"""

        element_info = {
            'key': element_key,
            'data': element_data,
            'parent_section': parent_section,
            'depth': depth,
            'is_section': 'Section' in element_data.get('label', ''),
            'children': [],
            'immediate_children': [],  # Level 1: Direct children to be evaluated
            'nested_children': [],     # Level 2+: Nested children for context only
            'evaluation_level': None   # Will be set to 0 for sections, 1 for immediate children
        }

        # If this is a section, track it as Level 0
        if element_info['is_section']:
            sections.append(element_info)
            element_info['evaluation_level'] = 0  # Level 0: Section evaluation
            current_section = element_info
        else:
            current_section = parent_section

        # Process children recursively
        children_data = element_data.get('children', [])
        for i, child in enumerate(children_data):
            child_key = f"{element_key}_child_{i}"
            child_info = process_element(child_key, child, current_section, depth + 1)
            element_info['children'].append(child_info)

            # For sections, categorize children by evaluation level
            if element_info['is_section']:
                # This is an immediate child (Level 1) - will be evaluated
                child_info['evaluation_level'] = 1  # Level 1: Immediate child evaluation
                element_info['immediate_children'].append(child_info)

                # Collect all nested children (Level 2+) for context only
                nested_children = get_all_nested_children_for_context(child_info)
                element_info['nested_children'].extend(nested_children)

        all_elements.append(element_info)
        return element_info

    # Process all root elements
    for element_key, element_data in coordinates_data.items():
        process_element(element_key, element_data)

    print(f"📊 Extracted {len(sections)} sections with two-level evaluation structure")
    print(f"🏗️ Total elements processed: {len(all_elements)}")

    # Print detailed section structure summary
    print(f"\n📋 Two-Level Evaluation Structure:")
    for i, section in enumerate(sections):
        section_label = section['data'].get('label', 'Unknown Section')
        immediate_count = len(section['immediate_children'])
        nested_count = len(section['nested_children'])
        print(f"  {i+1}. 📋 Level 0 - {section_label}:")
        print(f"     🔍 Level 1 - {immediate_count} immediate children (will be evaluated)")
        print(f"     📝 Level 2+ - {nested_count} nested children (context only)")

    return sections, all_elements

def get_all_nested_children_for_context(element_info):
    """Get all nested children (Level 2+) from an element for context only"""

    nested_children = []

    def collect_nested(element, current_depth=0):
        """Recursively collect all nested children for context"""
        for child in element.get('children', []):
            # Mark as context-only (not for individual evaluation)
            child['evaluation_level'] = 'context_only'
            nested_children.append(child)
            # Continue collecting from deeper levels
            collect_nested(child, current_depth + 1)

    collect_nested(element_info)
    return nested_children

def get_immediate_children_for_evaluation(section_info):
    """Get immediate children (Level 1) that need individual evaluation"""

    immediate_children = []

    for child in section_info.get('immediate_children', []):
        # Only include children that should be evaluated (not excluded)
        child_label = child.get('data', {}).get('label', '')
        if child_label not in config.EXCLUDED_LABELS:
            immediate_children.append(child)

    return immediate_children

def get_nested_children_context(section_info):
    """Get nested children (Level 2+) as context for evaluation"""

    context_children = []

    for child in section_info.get('nested_children', []):
        child_data = child.get('data', {})
        context_info = {
            'label': child_data.get('label', 'Unknown'),
            'coordinates': child_data.get('coordinates', {}),
            'depth': child.get('depth', 0),
            'evaluation_level': 'context_only'
        }
        context_children.append(context_info)

    return context_children

print("✅ Two-Level Evaluation System functions defined!")

class HeuristicEvaluationSystem:
    """Main system for conducting heuristic evaluations"""

    def __init__(self):
        self.evaluator = HeuristicEvaluator()
        self.coordinates_data = []
        self.element_info_data = {}
        self.evaluation_results = []

        print("✅ Heuristic Evaluation System initialized!")

    def load_data(self, coordinates_data=None, element_info_data=None):
        """Load data for evaluation"""
        if coordinates_data is None or element_info_data is None:
            # Try to load from configuration files
            coordinates_data, element_info_data = load_data_from_config()

            # Use fallback data if files are not available
            if coordinates_data is None or element_info_data is None:
                print("\n⚠️ Configuration files not found. Using fallback data for demonstration.")
                print("💡 You can upload your own files using the upload interface below.")
                coordinates_data, element_info_data = create_fallback_data()

        self.coordinates_data = coordinates_data
        self.element_info_data = element_info_data

        print(f"\n✅ Data loading complete!")
        print(f"📊 Coordinates: {len(self.coordinates_data)} elements")
        print(f"🏗️ Element info: {len(self.element_info_data)} elements")

        return True

    def perform_evaluation(self):
        """Perform heuristic evaluation on all loaded elements"""
        if not self.coordinates_data or not self.element_info_data:
            print("❌ No data loaded. Please load data first.")
            return False

        print("\n🚀 Starting Heuristic Evaluation...")
        print("=" * 60)

        self.evaluation_results = []

        for i, coord_data in enumerate(self.coordinates_data):
            # Check if element should be evaluated
            if not self.evaluator.should_evaluate_element(coord_data):
                print(f"⏭️ Skipping {coord_data.get('label', 'Unknown')} (excluded element)")
                continue

            # Get corresponding element info
            element_key = f"element_{coord_data.get('index', i) + 1}"
            element_data = self.element_info_data.get(element_key, {})

            print(f"\n🔍 Evaluating Element {i+1}: {coord_data.get('label', 'Unknown')}")
            print(f"📍 Position: ({coord_data.get('coordinates', {}).get('x', 0)}, {coord_data.get('coordinates', {}).get('y', 0)})")

            # Perform evaluation
            try:
                result = self.evaluator.evaluate_element(element_data, coord_data)
                self.evaluation_results.append(result)

                # Display quick summary
                status = result.get('evaluation_status', 'unknown')
                score = result.get('overall_score', 0)
                violations = len(result.get('violations', []))

                print(f"✅ Evaluation complete - Status: {status}, Score: {score}/100, Violations: {violations}")

            except Exception as e:
                print(f"❌ Error evaluating element: {str(e)}")
                continue

        print(f"\n🎉 Heuristic evaluation completed!")
        print(f"📊 Evaluated {len(self.evaluation_results)} elements")

        return True

    def generate_report(self):
        """Generate comprehensive heuristic evaluation report"""
        if not self.evaluation_results:
            print("❌ No evaluation results available. Please run evaluation first.")
            return None

        print("\n📋 HEURISTIC EVALUATION REPORT")
        print("=" * 80)

        # Overall statistics
        total_elements = len(self.evaluation_results)
        total_violations = sum(len(result.get('violations', [])) for result in self.evaluation_results)
        avg_score = sum(result.get('overall_score', 0) for result in self.evaluation_results) / total_elements if total_elements > 0 else 0

        print(f"\n📊 OVERALL STATISTICS:")
        print(f"   • Elements Evaluated: {total_elements}")
        print(f"   • Total Violations Found: {total_violations}")
        print(f"   • Average Usability Score: {avg_score:.1f}/100")

        # Severity breakdown
        severity_counts = {'high': 0, 'medium': 0, 'low': 0}
        for result in self.evaluation_results:
            for violation in result.get('violations', []):
                severity = violation.get('severity', 'medium').lower()
                if severity in severity_counts:
                    severity_counts[severity] += 1

        print(f"\n🚨 VIOLATION SEVERITY BREAKDOWN:")
        print(f"   • High Severity: {severity_counts['high']}")
        print(f"   • Medium Severity: {severity_counts['medium']}")
        print(f"   • Low Severity: {severity_counts['low']}")

        # Element-by-element results
        print(f"\n🔍 DETAILED ELEMENT ANALYSIS:")
        print("-" * 80)

        for i, result in enumerate(self.evaluation_results):
            element_info = result.get('element_info', {})
            label = element_info.get('label', 'Unknown')
            score = result.get('overall_score', 0)
            violations = result.get('violations', [])

            print(f"\n{i+1}. {label} (Score: {score}/100)")

            if violations:
                print(f"   ❌ Violations Found ({len(violations)}):")
                for violation in violations:
                    heuristic = violation.get('heuristic', 'Unknown')
                    severity = violation.get('severity', 'medium')
                    description = violation.get('violation', 'No description')
                    print(f"      • [{severity.upper()}] {heuristic}: {description}")
            else:
                print(f"   ✅ No violations found")

        # Top recommendations
        all_recommendations = []
        for result in self.evaluation_results:
            all_recommendations.extend(result.get('recommendations', []))

        if all_recommendations:
            print(f"\n💡 TOP RECOMMENDATIONS:")
            print("-" * 80)
            for i, rec in enumerate(all_recommendations[:10], 1):  # Top 10
                print(f"{i}. {rec}")

        print("\n" + "=" * 80)
        print("📋 End of Heuristic Evaluation Report")

        return {
            'total_elements': total_elements,
            'total_violations': total_violations,
            'average_score': avg_score,
            'severity_counts': severity_counts,
            'detailed_results': self.evaluation_results
        }

print("✅ HeuristicEvaluationSystem class defined!")

class SectionWiseHeuristicEvaluationSystem:
    """Enhanced system for conducting section-wise heuristic evaluations with child elements"""

    def __init__(self):
        self.evaluator = HeuristicEvaluator()
        self.coordinates_data = {}
        self.element_info_data = {}
        self.sections = []
        self.all_elements = []
        self.evaluation_results = []
        self.section_evaluations = []

        print("✅ Section-wise Heuristic Evaluation System initialized!")

    def load_hierarchical_data(self, coordinates_data=None, element_info_data=None):
        """Load hierarchical data for section-wise evaluation"""
        if coordinates_data is None or element_info_data is None:
            # Try to load from configuration files
            coordinates_data, element_info_data = load_hierarchical_data_from_config()

            # Use fallback data if files are not available
            if coordinates_data is None or element_info_data is None:
                print("\n⚠️ Configuration files not found. Using fallback data for demonstration.")
                print("💡 You can upload your own files using the upload interface below.")
                coordinates_data, element_info_data = create_fallback_data()

        self.coordinates_data = coordinates_data
        self.element_info_data = element_info_data

        # Extract sections and hierarchical structure
        if coordinates_data:
            self.sections, self.all_elements = extract_sections_and_children(coordinates_data)

        print(f"\n✅ Hierarchical data loading complete!")
        print(f"📊 Root elements: {len(self.coordinates_data)} elements")
        print(f"🏗️ Element info: {len(self.element_info_data)} elements")
        print(f"📋 Sections identified: {len(self.sections)} sections")
        print(f"🔍 Total elements in hierarchy: {len(self.all_elements)} elements")

        return True

    def evaluate_section_with_children(self, section_info, show_visual=True):
        """Evaluate a section considering all its child elements"""

        section_key = section_info['key']
        section_data = section_info['data']
        section_label = section_data.get('label', 'Unknown Section')

        print(f"\n🏗️ Evaluating Section: {section_label}")
        print(f"📍 Section Key: {section_key}")
        print(f"📊 Section Position: ({section_data.get('coordinates', {}).get('x', 0)}, {section_data.get('coordinates', {}).get('y', 0)})")

        # Get all child elements for this section
        child_elements = get_all_child_elements(section_info, include_excluded=False)

        print(f"👥 Child elements found: {len(child_elements)}")

        # Evaluate the section itself
        section_element_data = self.element_info_data.get(section_key, {})

        # Create coordinate data for section evaluation
        section_coord_data = {
            'coordinates': section_data.get('coordinates', {}),
            'label': section_label,
            'index': section_key
        }

        # Evaluate section with context of its children
        section_evaluation = self._evaluate_section_with_context(
            section_element_data, section_coord_data, child_elements, show_visual
        )

        # Evaluate each child element within section context
        child_evaluations = []
        for i, child in enumerate(child_elements):
            child_key = child['key']
            child_data = child['data']
            child_label = child_data.get('label', 'Unknown Child')

            print(f"  🔍 Evaluating child {i+1}/{len(child_elements)}: {child_label}")

            # Get element info for child
            child_element_data = self.element_info_data.get(child_key, {})

            # Create coordinate data for child evaluation
            child_coord_data = {
                'coordinates': child_data.get('coordinates', {}),
                'label': child_label,
                'index': child_key
            }

            # Evaluate child with section context
            child_evaluation = self._evaluate_child_with_section_context(
                child_element_data, child_coord_data, section_info, show_visual
            )

            child_evaluations.append(child_evaluation)

        # Combine section and child evaluations
        combined_evaluation = {
            'section_info': section_info,
            'section_evaluation': section_evaluation,
            'child_evaluations': child_evaluations,
            'total_children': len(child_elements),
            'section_score': section_evaluation.get('overall_score', 0),
            'average_child_score': sum(ce.get('overall_score', 0) for ce in child_evaluations) / len(child_evaluations) if child_evaluations else 0,
            'total_violations': len(section_evaluation.get('violations', [])) + sum(len(ce.get('violations', [])) for ce in child_evaluations)
        }

        print(f"  ✅ Section evaluation complete - Section Score: {combined_evaluation['section_score']}/100")
        print(f"  📊 Average Child Score: {combined_evaluation['average_child_score']:.1f}/100")
        print(f"  🚨 Total Violations: {combined_evaluation['total_violations']}")

        return combined_evaluation

    def load_two_level_hierarchical_data(self, coordinates_data=None, element_info_data=None):
        """Load hierarchical data for two-level section-wise evaluation"""
        if coordinates_data is None or element_info_data is None:
            # Try to load from configuration files
            coordinates_data, element_info_data = load_hierarchical_data_from_config()

            # Use fallback data if files are not available
            if coordinates_data is None or element_info_data is None:
                print("\n⚠️ Configuration files not found. Using fallback data for demonstration.")
                print("💡 You can upload your own files using the upload interface below.")
                coordinates_data, element_info_data = create_fallback_data()

        self.coordinates_data = coordinates_data
        self.element_info_data = element_info_data

        # Extract sections with two-level hierarchical structure
        if coordinates_data:
            self.sections, self.all_elements = extract_sections_with_two_levels(coordinates_data)

        print(f"\n✅ Two-Level hierarchical data loading complete!")
        print(f"📊 Root elements: {len(self.coordinates_data)} elements")
        print(f"🏗️ Element info: {len(self.element_info_data)} elements")
        print(f"📋 Sections identified: {len(self.sections)} sections")
        print(f"🔍 Total elements in hierarchy: {len(self.all_elements)} elements")

        return True

    def evaluate_section_two_level(self, section_info, show_visual=True):
        """Evaluate a section using two-level approach: Level 0 (section) and Level 1 (immediate children)"""

        section_key = section_info['key']
        section_data = section_info['data']
        section_label = section_data.get('label', 'Unknown Section')

        print(f"\n🏗️ Two-Level Evaluation for Section: {section_label}")
        print(f"📍 Section Key: {section_key}")
        print(f"📊 Section Position: ({section_data.get('coordinates', {}).get('x', 0)}, {section_data.get('coordinates', {}).get('y', 0)})")

        # Get immediate children (Level 1) for evaluation
        immediate_children = get_immediate_children_for_evaluation(section_info)

        # Get nested children (Level 2+) for context only
        nested_children_context = get_nested_children_context(section_info)

        print(f"🔍 Level 1 - Immediate children to evaluate: {len(immediate_children)}")
        print(f"📝 Level 2+ - Nested children for context: {len(nested_children_context)}")

        # Show visual analysis for the section if available
        if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:
            try:
                print(f"\n🖼️ Level 0 Visual Analysis - Section: {section_label}")
                section_coords = section_data.get('coordinates', {})
                highlighted, cropped = self.evaluator.visual_analyzer.display_element_analysis(
                    section_coords, f"Level 0 - Section: {section_label}"
                )
            except Exception as e:
                print(f"⚠️ Could not display section visual analysis: {str(e)}")

        # LEVEL 0: Evaluate the section itself
        print(f"\n📋 Level 0 Evaluation - Section as a whole")
        section_element_data = self.element_info_data.get(section_key, {})

        section_coord_data = {
            'coordinates': section_data.get('coordinates', {}),
            'label': section_label,
            'index': section_key
        }

        # Evaluate section with context of immediate and nested children
        section_evaluation = self._evaluate_section_two_level(
            section_element_data, section_coord_data, immediate_children, nested_children_context, show_visual
        )

        # LEVEL 1: Evaluate immediate children individually
        print(f"\n🔍 Level 1 Evaluation - Immediate children")
        level1_evaluations = []

        for i, child in enumerate(immediate_children):
            child_key = child['key']
            child_data = child['data']
            child_label = child_data.get('label', 'Unknown Child')

            print(f"  🔍 Level 1 - Evaluating {i+1}/{len(immediate_children)}: {child_label}")

            # Show visual analysis for immediate child if available
            if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:
                try:
                    print(f"    🖼️ Level 1 Visual Analysis - Child: {child_label}")
                    child_coords = child_data.get('coordinates', {})
                    child_highlighted, child_cropped = self.evaluator.visual_analyzer.display_element_analysis(
                        child_coords, f"Level 1 - Child: {child_label}"
                    )
                except Exception as e:
                    print(f"    ⚠️ Could not display child visual analysis: {str(e)}")

            # Get nested children of this immediate child for context
            child_nested_context = get_all_nested_children_for_context(child)

            # Get element info for immediate child
            child_element_data = self.element_info_data.get(child_key, {})

            child_coord_data = {
                'coordinates': child_data.get('coordinates', {}),
                'label': child_label,
                'index': child_key
            }

            # Evaluate immediate child with section context and its nested children as context
            child_evaluation = self._evaluate_level1_child_with_context(
                child_element_data, child_coord_data, section_info, child_nested_context, show_visual
            )

            level1_evaluations.append(child_evaluation)

        # Combine Level 0 and Level 1 evaluations
        combined_evaluation = {
            'section_info': section_info,
            'level0_evaluation': section_evaluation,  # Section evaluation
            'level1_evaluations': level1_evaluations,  # Immediate children evaluations
            'total_level1_children': len(immediate_children),
            'total_context_children': len(nested_children_context),
            'level0_score': section_evaluation.get('overall_score', 0),
            'average_level1_score': sum(ce.get('overall_score', 0) for ce in level1_evaluations) / len(level1_evaluations) if level1_evaluations else 0,
            'total_violations': len(section_evaluation.get('violations', [])) + sum(len(ce.get('violations', [])) for ce in level1_evaluations),
            'evaluation_approach': 'two_level',
            'has_visual_context': show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None
        }

        print(f"\n  ✅ Two-Level evaluation complete:")
        print(f"    📋 Level 0 (Section) Score: {combined_evaluation['level0_score']}/100")
        print(f"    🔍 Level 1 (Avg Children) Score: {combined_evaluation['average_level1_score']:.1f}/100")
        print(f"    🚨 Total Violations: {combined_evaluation['total_violations']}")
        print(f"    📊 Children Evaluated: {combined_evaluation['total_level1_children']}")
        print(f"    📝 Context Children: {combined_evaluation['total_context_children']}")

        return combined_evaluation

print("✅ SectionWiseHeuristicEvaluationSystem class defined!")

# Enhanced visual evaluation methods for section-wise system
def evaluate_section_with_children_visual(self, section_info, show_visual=True):
    """Evaluate a section with visual-enhanced analysis considering all its child elements"""

    section_key = section_info['key']
    section_data = section_info['data']
    section_label = section_data.get('label', 'Unknown Section')

    print(f"\n🏗️ Evaluating Section with Visual Analysis: {section_label}")
    print(f"📍 Section Key: {section_key}")
    print(f"📊 Section Position: ({section_data.get('coordinates', {}).get('x', 0)}, {section_data.get('coordinates', {}).get('y', 0)})")

    # Get all child elements for this section
    child_elements = get_all_child_elements(section_info, include_excluded=False)

    print(f"👥 Child elements found: {len(child_elements)}")

    # Show visual analysis for the section if available
    if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:
        try:
            print(f"\n🖼️ Visual Analysis for Section: {section_label}")
            section_coords = section_data.get('coordinates', {})
            highlighted, cropped = self.evaluator.visual_analyzer.display_element_analysis(
                section_coords, f"Section: {section_label}"
            )
        except Exception as e:
            print(f"⚠️ Could not display section visual analysis: {str(e)}")

    # Evaluate the section itself with visual context
    section_element_data = self.element_info_data.get(section_key, {})

    # Create coordinate data for section evaluation
    section_coord_data = {
        'coordinates': section_data.get('coordinates', {}),
        'label': section_label,
        'index': section_key
    }

    # Evaluate section with visual context and children information
    section_evaluation = self._evaluate_section_with_visual_context(
        section_element_data, section_coord_data, child_elements, show_visual
    )

    # Determine evaluation strategy for children
    child_evaluations = []
    children_need_individual_eval = self._should_evaluate_children_individually(
        section_evaluation, child_elements
    )

    if children_need_individual_eval:
        print(f"  🔍 Individual evaluation needed for child elements")
        # Evaluate each child element individually with visual analysis
        for i, child in enumerate(child_elements):
            child_key = child['key']
            child_data = child['data']
            child_label = child_data.get('label', 'Unknown Child')

            print(f"    🔍 Evaluating child {i+1}/{len(child_elements)}: {child_label}")

            # Show visual analysis for child if available
            if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:
                try:
                    print(f"      🖼️ Visual Analysis for Child: {child_label}")
                    child_coords = child_data.get('coordinates', {})
                    child_highlighted, child_cropped = self.evaluator.visual_analyzer.display_element_analysis(
                        child_coords, f"Child: {child_label}"
                    )
                except Exception as e:
                    print(f"      ⚠️ Could not display child visual analysis: {str(e)}")

            # Get element info for child
            child_element_data = self.element_info_data.get(child_key, {})

            # Create coordinate data for child evaluation
            child_coord_data = {
                'coordinates': child_data.get('coordinates', {}),
                'label': child_label,
                'index': child_key
            }

            # Evaluate child with visual context and section context
            child_evaluation = self._evaluate_child_with_visual_section_context(
                child_element_data, child_coord_data, section_info, show_visual
            )

            child_evaluations.append(child_evaluation)
    else:
        print(f"  📋 Section-based evaluation sufficient for child elements")
        # Create summary evaluations based on section analysis
        for child in child_elements:
            child_summary = self._create_child_summary_from_section(child, section_evaluation)
            child_evaluations.append(child_summary)

    # Combine section and child evaluations
    combined_evaluation = {
        'section_info': section_info,
        'section_evaluation': section_evaluation,
        'child_evaluations': child_evaluations,
        'total_children': len(child_elements),
        'section_score': section_evaluation.get('overall_score', 0),
        'average_child_score': sum(ce.get('overall_score', 0) for ce in child_evaluations) / len(child_evaluations) if child_evaluations else 0,
        'total_violations': len(section_evaluation.get('violations', [])) + sum(len(ce.get('violations', [])) for ce in child_evaluations),
        'evaluation_strategy': 'individual_children' if children_need_individual_eval else 'section_based',
        'has_visual_context': show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None
    }

    print(f"  ✅ Section evaluation complete - Section Score: {combined_evaluation['section_score']}/100")
    print(f"  📊 Average Child Score: {combined_evaluation['average_child_score']:.1f}/100")
    print(f"  🚨 Total Violations: {combined_evaluation['total_violations']}")
    print(f"  🎯 Evaluation Strategy: {combined_evaluation['evaluation_strategy']}")
    print(f"  🖼️ Visual Context: {'Available' if combined_evaluation['has_visual_context'] else 'Not Available'}")

    return combined_evaluation

# Bind the visual-enhanced method to the class
SectionWiseHeuristicEvaluationSystem.evaluate_section_with_children_visual = evaluate_section_with_children_visual

print("✅ Visual-enhanced section evaluation method added!")

# Supporting methods for visual-enhanced section evaluation
def _evaluate_section_with_visual_context(self, section_element_data, section_coord_data, child_elements, show_visual=True):
    """Evaluate a section with visual context and child elements"""

    # Create enhanced prompt that includes child element context and visual analysis
    child_context = []
    for child in child_elements:
        child_data = child['data']
        child_context.append({
            'label': child_data.get('label', 'Unknown'),
            'coordinates': child_data.get('coordinates', {}),
            'depth': child.get('depth', 0)
        })

    # Enhanced element info with child context and visual data
    enhanced_element_info = {
        "index": section_coord_data.get("index", -1),
        "label": section_coord_data.get("label", "Unknown"),
        "coordinates": section_coord_data.get("coordinates", {}),
        "tag": section_element_data.get("tag", "unknown"),
        "text": section_element_data.get("text", ""),
        "css_selector": section_element_data.get("cssSelector", ""),
        "xpath": section_element_data.get("xpath", ""),
        "computed_style": section_element_data.get("computedStyle", {}),
        "attributes": section_element_data.get("attributes", {}),
        "child_elements_context": child_context,
        "total_children": len(child_elements),
        "is_section": True,
        "has_visual_context": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None
    }

    # Use visual-enhanced evaluation if available
    if enhanced_element_info["has_visual_context"]:
        try:
            # Use the visual-enhanced evaluation method
            evaluation_result = self.evaluator.evaluate_element_with_visual(
                enhanced_element_info, section_coord_data, show_visual=show_visual
            )
            evaluation_result['evaluation_type'] = 'section_with_visual_context'
            return evaluation_result
        except Exception as e:
            print(f"⚠️ Visual evaluation failed, falling back to standard evaluation: {str(e)}")

    # Fallback to standard evaluation with enhanced context
    prompt = self._create_section_evaluation_prompt(enhanced_element_info)

    try:
        # Get Gemini evaluation
        response = self.evaluator.model.generate_content(prompt)

        # Parse response
        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)
        evaluation_result['evaluation_type'] = 'section_with_context'

        return evaluation_result

    except Exception as e:
        return {
            "element_info": enhanced_element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in section evaluation: {str(e)}",
            "evaluation_status": "error",
            "evaluation_type": "section_with_context"
        }

def _evaluate_child_with_visual_section_context(self, child_element_data, child_coord_data, section_info, show_visual=True):
    """Evaluate a child element with visual context and section context"""

    section_data = section_info['data']

    # Enhanced element info with section context and visual data
    enhanced_element_info = {
        "index": child_coord_data.get("index", -1),
        "label": child_coord_data.get("label", "Unknown"),
        "coordinates": child_coord_data.get("coordinates", {}),
        "tag": child_element_data.get("tag", "unknown"),
        "text": child_element_data.get("text", ""),
        "css_selector": child_element_data.get("cssSelector", ""),
        "xpath": child_element_data.get("xpath", ""),
        "computed_style": child_element_data.get("computedStyle", {}),
        "attributes": child_element_data.get("attributes", {}),
        "parent_section_context": {
            'section_label': section_data.get('label', 'Unknown Section'),
            'section_coordinates': section_data.get('coordinates', {}),
            'section_key': section_info['key']
        },
        "is_child_element": True,
        "has_visual_context": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None
    }

    # Use visual-enhanced evaluation if available
    if enhanced_element_info["has_visual_context"]:
        try:
            # Use the visual-enhanced evaluation method
            evaluation_result = self.evaluator.evaluate_element_with_visual(
                enhanced_element_info, child_coord_data, show_visual=show_visual
            )
            evaluation_result['evaluation_type'] = 'child_with_visual_section_context'
            return evaluation_result
        except Exception as e:
            print(f"⚠️ Visual evaluation failed for child, falling back to standard evaluation: {str(e)}")

    # Fallback to standard evaluation with enhanced context
    prompt = self._create_child_evaluation_prompt(enhanced_element_info)

    try:
        # Get Gemini evaluation
        response = self.evaluator.model.generate_content(prompt)

        # Parse response
        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)
        evaluation_result['evaluation_type'] = 'child_with_section_context'

        return evaluation_result

    except Exception as e:
        return {
            "element_info": enhanced_element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in child evaluation: {str(e)}",
            "evaluation_status": "error",
            "evaluation_type": "child_with_section_context"
        }

# Bind the visual-enhanced support methods to the class
SectionWiseHeuristicEvaluationSystem._evaluate_section_with_visual_context = _evaluate_section_with_visual_context
SectionWiseHeuristicEvaluationSystem._evaluate_child_with_visual_section_context = _evaluate_child_with_visual_section_context

print("✅ Visual-enhanced support methods added!")

# Decision-making methods for evaluation strategy
def _should_evaluate_children_individually(self, section_evaluation, child_elements):
    """Determine if child elements need individual evaluation or if section-based evaluation is sufficient"""

    # Criteria for individual child evaluation:
    # 1. Section has high-severity violations that might affect children differently
    # 2. Large number of diverse child elements
    # 3. Section evaluation indicates inconsistencies
    # 4. Child elements have complex interactions

    section_violations = section_evaluation.get('violations', [])
    section_score = section_evaluation.get('overall_score', 100)

    # Check for high-severity violations in section
    high_severity_violations = [v for v in section_violations if v.get('severity', '').lower() == 'high']

    # Check for inconsistency indicators
    inconsistency_keywords = ['inconsistent', 'unclear', 'confusing', 'mixed', 'varied', 'different']
    section_analysis = section_evaluation.get('gemini_analysis', '').lower()
    has_inconsistency_indicators = any(keyword in section_analysis for keyword in inconsistency_keywords)

    # Decision criteria
    criteria = {
        'high_severity_violations': len(high_severity_violations) > 0,
        'low_section_score': section_score < 70,
        'many_children': len(child_elements) > 5,
        'diverse_children': len(set(child['data'].get('label', '') for child in child_elements)) > 3,
        'inconsistency_indicators': has_inconsistency_indicators
    }

    # Count positive criteria
    positive_criteria = sum(criteria.values())

    # Decision logic: evaluate individually if 2 or more criteria are met
    should_evaluate_individually = positive_criteria >= 2

    print(f"    📊 Evaluation Decision Criteria:")
    for criterion, value in criteria.items():
        status = "✅" if value else "❌"
        print(f"      {status} {criterion.replace('_', ' ').title()}: {value}")

    print(f"    🎯 Decision: {'Individual evaluation' if should_evaluate_individually else 'Section-based evaluation'} ({positive_criteria}/5 criteria met)")

    return should_evaluate_individually

def _create_child_summary_from_section(self, child, section_evaluation):
    """Create a summary evaluation for a child based on section evaluation"""

    child_data = child['data']
    child_label = child_data.get('label', 'Unknown Child')

    # Inherit section characteristics but adjust for child-specific factors
    section_score = section_evaluation.get('overall_score', 100)
    section_violations = section_evaluation.get('violations', [])

    # Adjust score based on child element type and position
    child_score_adjustment = 0

    # Interactive elements might have different usability considerations
    interactive_elements = ['Button', 'Link', 'Input', 'Select', 'Checkbox', 'Radio']
    if any(elem in child_label for elem in interactive_elements):
        child_score_adjustment = -5  # Slightly more critical for interactive elements

    # Text elements might be less critical
    text_elements = ['Text', 'Heading', 'Label', 'Paragraph']
    if any(elem in child_label for elem in text_elements):
        child_score_adjustment = 5  # Slightly less critical for text elements

    adjusted_score = max(0, min(100, section_score + child_score_adjustment))

    # Create child-specific violations based on section violations
    child_violations = []
    for violation in section_violations:
        if violation.get('affects_children', True):  # Assume affects children unless specified
            child_violation = {
                'heuristic': violation.get('heuristic', 'Unknown'),
                'violation': f"Inherited from section: {violation.get('violation', 'Unknown issue')}",
                'reason': f"Child element affected by section-level issue: {violation.get('reason', 'No details')}",
                'severity': violation.get('severity', 'medium'),
                'recommendation': f"Address section-level issue affecting this {child_label}",
                'inherited_from_section': True
            }
            child_violations.append(child_violation)

    return {
        'element_info': {
            'label': child_label,
            'coordinates': child_data.get('coordinates', {}),
            'index': child['key']
        },
        'violations': child_violations,
        'passed_checks': section_evaluation.get('passed_checks', []),
        'overall_score': adjusted_score,
        'recommendations': [f"Ensure {child_label} follows section-wide improvements"],
        'gemini_analysis': f"Summary evaluation based on section analysis. {child_label} inherits section characteristics with minor adjustments.",
        'evaluation_status': 'section_based_summary',
        'evaluation_type': 'child_summary_from_section'
    }

# Bind the decision-making methods to the class
SectionWiseHeuristicEvaluationSystem._should_evaluate_children_individually = _should_evaluate_children_individually
SectionWiseHeuristicEvaluationSystem._create_child_summary_from_section = _create_child_summary_from_section

print("✅ Decision-making methods for evaluation strategy added!")

# Two-Level Evaluation Methods
def _evaluate_section_two_level(self, section_element_data, section_coord_data, immediate_children, nested_children_context, show_visual=True):
    """Evaluate a section (Level 0) with context of immediate and nested children"""

    # Create enhanced prompt that includes both immediate and nested children context
    immediate_context = []
    for child in immediate_children:
        child_data = child['data']
        immediate_context.append({
            'label': child_data.get('label', 'Unknown'),
            'coordinates': child_data.get('coordinates', {}),
            'evaluation_level': 1,  # Will be evaluated individually
            'depth': child.get('depth', 0)
        })

    # Enhanced element info with two-level context and visual data
    enhanced_element_info = {
        "index": section_coord_data.get("index", -1),
        "label": section_coord_data.get("label", "Unknown"),
        "coordinates": section_coord_data.get("coordinates", {}),
        "tag": section_element_data.get("tag", "unknown"),
        "text": section_element_data.get("text", ""),
        "css_selector": section_element_data.get("cssSelector", ""),
        "xpath": section_element_data.get("xpath", ""),
        "computed_style": section_element_data.get("computedStyle", {}),
        "attributes": section_element_data.get("attributes", {}),
        "immediate_children_context": immediate_context,  # Level 1 children
        "nested_children_context": nested_children_context,  # Level 2+ children
        "total_immediate_children": len(immediate_children),
        "total_nested_children": len(nested_children_context),
        "is_section": True,
        "evaluation_level": 0,  # Level 0 evaluation
        "has_visual_context": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None
    }

    # Use visual-enhanced evaluation if available
    if enhanced_element_info["has_visual_context"]:
        try:
            # Use the visual-enhanced evaluation method
            evaluation_result = self.evaluator.evaluate_element_with_visual(
                enhanced_element_info, section_coord_data, show_visual=show_visual
            )
            evaluation_result['evaluation_type'] = 'level0_section_with_visual_context'
            return evaluation_result
        except Exception as e:
            print(f"⚠️ Visual evaluation failed, falling back to standard evaluation: {str(e)}")

    # Fallback to standard evaluation with enhanced context
    prompt = self._create_level0_section_evaluation_prompt(enhanced_element_info)

    try:
        # Get Gemini evaluation
        response = self.evaluator.model.generate_content(prompt)

        # Parse response
        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)
        evaluation_result['evaluation_type'] = 'level0_section_evaluation'

        return evaluation_result

    except Exception as e:
        return {
            "element_info": enhanced_element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in Level 0 section evaluation: {str(e)}",
            "evaluation_status": "error",
            "evaluation_type": "level0_section_evaluation"
        }

def _evaluate_level1_child_with_context(self, child_element_data, child_coord_data, section_info, child_nested_context, show_visual=True):
    """Evaluate an immediate child (Level 1) with section context and its nested children as context"""

    section_data = section_info['data']

    # Enhanced element info with section context, nested children context, and visual data
    enhanced_element_info = {
        "index": child_coord_data.get("index", -1),
        "label": child_coord_data.get("label", "Unknown"),
        "coordinates": child_coord_data.get("coordinates", {}),
        "tag": child_element_data.get("tag", "unknown"),
        "text": child_element_data.get("text", ""),
        "css_selector": child_element_data.get("cssSelector", ""),
        "xpath": child_element_data.get("xpath", ""),
        "computed_style": child_element_data.get("computedStyle", {}),
        "attributes": child_element_data.get("attributes", {}),
        "parent_section_context": {
            'section_label': section_data.get('label', 'Unknown Section'),
            'section_coordinates': section_data.get('coordinates', {}),
            'section_key': section_info['key']
        },
        "nested_children_context": child_nested_context,  # Level 2+ children for context
        "total_nested_children": len(child_nested_context),
        "is_immediate_child": True,
        "evaluation_level": 1,  # Level 1 evaluation
        "has_visual_context": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None
    }

    # Use visual-enhanced evaluation if available
    if enhanced_element_info["has_visual_context"]:
        try:
            # Use the visual-enhanced evaluation method
            evaluation_result = self.evaluator.evaluate_element_with_visual(
                enhanced_element_info, child_coord_data, show_visual=show_visual
            )
            evaluation_result['evaluation_type'] = 'level1_child_with_visual_context'
            return evaluation_result
        except Exception as e:
            print(f"⚠️ Visual evaluation failed for Level 1 child, falling back to standard evaluation: {str(e)}")

    # Fallback to standard evaluation with enhanced context
    prompt = self._create_level1_child_evaluation_prompt(enhanced_element_info)

    try:
        # Get Gemini evaluation
        response = self.evaluator.model.generate_content(prompt)

        # Parse response
        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)
        evaluation_result['evaluation_type'] = 'level1_child_evaluation'

        return evaluation_result

    except Exception as e:
        return {
            "element_info": enhanced_element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in Level 1 child evaluation: {str(e)}",
            "evaluation_status": "error",
            "evaluation_type": "level1_child_evaluation"
        }

# Bind the two-level evaluation methods to the class
SectionWiseHeuristicEvaluationSystem._evaluate_section_two_level = _evaluate_section_two_level
SectionWiseHeuristicEvaluationSystem._evaluate_level1_child_with_context = _evaluate_level1_child_with_context

print("✅ Two-Level evaluation methods added!")

# Specialized prompts for two-level evaluation
def _create_level0_section_evaluation_prompt(self, element_info):
    """Create evaluation prompt for Level 0 (section) evaluation"""

    element_json = json.dumps(element_info, indent=2)

    prompt = f"""
You are a UX expert conducting a Level 0 (Section-level) heuristic evaluation using Nielsen's 10 usability principles.

HEURISTIC EVALUATION PRINCIPLES:
{self.evaluator.heuristic_principles}

LEVEL 0 SECTION TO EVALUATE:
{element_json}

TWO-LEVEL EVALUATION CONTEXT:
• Level 0 (Current): Evaluate the SECTION as a whole organizational unit
• Level 1 (Separate): Immediate children will be evaluated individually
• Level 2+ (Context): Nested children provide context but are NOT evaluated individually

LEVEL 0 SECTION EVALUATION FOCUS:
Evaluate this section as a CONTAINER and ORGANIZATIONAL UNIT, considering:

1. **Section Organization**: How well does the section organize and group related content?
2. **Visual Hierarchy**: Does the section create clear visual structure and relationships?
3. **Information Architecture**: Is the section's role in the overall page structure clear?
4. **Section Coherence**: Do all immediate children serve the section's purpose?
5. **Layout Effectiveness**: Does the section layout support user understanding?
6. **Section Consistency**: Are patterns consistent within this section?
7. **Accessibility Structure**: Is the section structure accessible and semantic?
8. **Content Grouping**: Are related elements properly grouped within the section?

IMPORTANT EVALUATION GUIDELINES:
• Focus on the SECTION'S ORGANIZATIONAL ROLE, not individual element details
• Consider how immediate children work together as a group
• Use nested children as context for understanding section complexity
• Evaluate section-level patterns, consistency, and structure
• Assess the section's contribution to overall page usability

RESPONSE FORMAT (JSON):
{{
    "violations": [
        {{
            "heuristic": "Heuristic Name",
            "violation": "Section-level organizational issue",
            "reason": "Detailed explanation focusing on section structure and organization",
            "severity": "high|medium|low",
            "recommendation": "Section-level improvement recommendation",
            "affects_immediate_children": "How this affects Level 1 children evaluation"
        }}
    ],
    "passed_checks": [
        "List of heuristic names that this section passes at the organizational level"
    ],
    "overall_score": 85,
    "summary": "Brief assessment of section's organizational effectiveness",
    "key_recommendations": [
        "Most important section-level organizational improvements"
    ],
    "section_strengths": [
        "What this section does well organizationally"
    ],
    "immediate_children_guidance": "Guidance for evaluating immediate children based on section context"
}}

IMPORTANT: Return ONLY valid JSON. Focus on SECTION-LEVEL organizational evaluation.
"""
    return prompt

def _create_level1_child_evaluation_prompt(self, element_info):
    """Create evaluation prompt for Level 1 (immediate child) evaluation"""

    element_json = json.dumps(element_info, indent=2)

    prompt = f"""
You are a UX expert conducting a Level 1 (Immediate Child) heuristic evaluation using Nielsen's 10 usability principles.

HEURISTIC EVALUATION PRINCIPLES:
{self.evaluator.heuristic_principles}

LEVEL 1 IMMEDIATE CHILD TO EVALUATE:
{element_json}

TWO-LEVEL EVALUATION CONTEXT:
• Level 0 (Parent): Section has been evaluated separately as organizational unit
• Level 1 (Current): Evaluate this IMMEDIATE CHILD individually
• Level 2+ (Context): Nested children provide context but are NOT evaluated individually

LEVEL 1 IMMEDIATE CHILD EVALUATION FOCUS:
Evaluate this immediate child as an INDIVIDUAL FUNCTIONAL ELEMENT, considering:

1. **Individual Functionality**: Does this element work well on its own?
2. **Section Integration**: How well does it fit within its parent section?
3. **User Interaction**: Are interaction patterns clear and consistent?
4. **Visual Design**: Is the element visually appropriate and accessible?
5. **Content Quality**: Is the content clear, helpful, and well-presented?
6. **Behavioral Consistency**: Does it behave consistently with similar elements?
7. **Accessibility**: Is the element accessible to all users?
8. **Nested Content Context**: How do its nested children support its function?

IMPORTANT EVALUATION GUIDELINES:
• Focus on this ELEMENT'S INDIVIDUAL USABILITY and functionality
• Consider how it integrates with its parent section context
• Use nested children as context for understanding element complexity
• Evaluate element-specific patterns, interactions, and usability
• Assess the element's individual contribution to user experience

RESPONSE FORMAT (JSON):
{{
    "violations": [
        {{
            "heuristic": "Heuristic Name",
            "violation": "Individual element usability issue",
            "reason": "Detailed explanation focusing on element functionality and usability",
            "severity": "high|medium|low",
            "recommendation": "Element-specific improvement recommendation",
            "section_context_impact": "How this relates to the parent section context"
        }}
    ],
    "passed_checks": [
        "List of heuristic names that this element passes individually"
    ],
    "overall_score": 85,
    "summary": "Brief assessment of element's individual usability",
    "key_recommendations": [
        "Most important element-specific improvements"
    ],
    "element_strengths": [
        "What this element does well individually"
    ],
    "section_integration": "How well this element integrates with its parent section",
    "nested_children_utilization": "How effectively nested children support this element's function"
}}

IMPORTANT: Return ONLY valid JSON. Focus on INDIVIDUAL ELEMENT usability evaluation.
"""
    return prompt

# Bind the two-level prompt methods to the class
SectionWiseHeuristicEvaluationSystem._create_level0_section_evaluation_prompt = _create_level0_section_evaluation_prompt
SectionWiseHeuristicEvaluationSystem._create_level1_child_evaluation_prompt = _create_level1_child_evaluation_prompt

print("✅ Two-Level evaluation prompt methods added!")

# Add evaluation methods to SectionWiseHeuristicEvaluationSystem class
def _evaluate_section_with_context(self, section_element_data, section_coord_data, child_elements, show_visual=True):
    """Evaluate a section with context of its child elements"""

    # Create enhanced prompt that includes child element context
    child_context = []
    for child in child_elements:
        child_data = child['data']
        child_context.append({
            'label': child_data.get('label', 'Unknown'),
            'coordinates': child_data.get('coordinates', {}),
            'depth': child.get('depth', 0)
        })

    # Enhanced element info with child context
    enhanced_element_info = {
        "index": section_coord_data.get("index", -1),
        "label": section_coord_data.get("label", "Unknown"),
        "coordinates": section_coord_data.get("coordinates", {}),
        "tag": section_element_data.get("tag", "unknown"),
        "text": section_element_data.get("text", ""),
        "css_selector": section_element_data.get("cssSelector", ""),
        "xpath": section_element_data.get("xpath", ""),
        "computed_style": section_element_data.get("computedStyle", {}),
        "attributes": section_element_data.get("attributes", {}),
        "child_elements_context": child_context,
        "total_children": len(child_elements),
        "is_section": True
    }

    # Create section-specific evaluation prompt
    prompt = self._create_section_evaluation_prompt(enhanced_element_info)

    try:
        # Get Gemini evaluation
        response = self.evaluator.model.generate_content(prompt)

        # Parse response
        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)
        evaluation_result['evaluation_type'] = 'section_with_context'

        return evaluation_result

    except Exception as e:
        return {
            "element_info": enhanced_element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in section evaluation: {str(e)}",
            "evaluation_status": "error",
            "evaluation_type": "section_with_context"
        }

def _evaluate_child_with_section_context(self, child_element_data, child_coord_data, section_info, show_visual=True):
    """Evaluate a child element with context of its parent section"""

    section_data = section_info['data']

    # Enhanced element info with section context
    enhanced_element_info = {
        "index": child_coord_data.get("index", -1),
        "label": child_coord_data.get("label", "Unknown"),
        "coordinates": child_coord_data.get("coordinates", {}),
        "tag": child_element_data.get("tag", "unknown"),
        "text": child_element_data.get("text", ""),
        "css_selector": child_element_data.get("cssSelector", ""),
        "xpath": child_element_data.get("xpath", ""),
        "computed_style": child_element_data.get("computedStyle", {}),
        "attributes": child_element_data.get("attributes", {}),
        "parent_section_context": {
            'section_label': section_data.get('label', 'Unknown Section'),
            'section_coordinates': section_data.get('coordinates', {}),
            'section_key': section_info['key']
        },
        "is_child_element": True
    }

    # Create child-specific evaluation prompt
    prompt = self._create_child_evaluation_prompt(enhanced_element_info)

    try:
        # Get Gemini evaluation
        response = self.evaluator.model.generate_content(prompt)

        # Parse response
        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)
        evaluation_result['evaluation_type'] = 'child_with_section_context'

        return evaluation_result

    except Exception as e:
        return {
            "element_info": enhanced_element_info,
            "violations": [],
            "passed_checks": [],
            "overall_score": 0,
            "recommendations": [],
            "gemini_analysis": f"Error in child evaluation: {str(e)}",
            "evaluation_status": "error",
            "evaluation_type": "child_with_section_context"
        }

# Bind methods to the SectionWiseHeuristicEvaluationSystem class
SectionWiseHeuristicEvaluationSystem._evaluate_section_with_context = _evaluate_section_with_context
SectionWiseHeuristicEvaluationSystem._evaluate_child_with_section_context = _evaluate_child_with_section_context

print("✅ Section-wise evaluation methods added!")

# Add prompt creation methods for section-wise evaluation
def _create_section_evaluation_prompt(self, element_info):
    """Create evaluation prompt for sections with child element context"""

    element_json = json.dumps(element_info, indent=2)

    prompt = f"""
You are a UX expert conducting a comprehensive heuristic evaluation of a UI SECTION with its child elements.

HEURISTIC EVALUATION PRINCIPLES:
{self.evaluator.heuristic_principles}

SECTION TO EVALUATE (with child elements context):
{element_json}

SECTION-WISE EVALUATION TASK:
Analyze this UI section against ALL 10 Nielsen's usability heuristics, considering:
1. The section as a container and organizational unit
2. How well it groups and organizes its child elements
3. The coherence and consistency within the section
4. The section's role in the overall page hierarchy
5. How child elements work together within this section

SECTION-SPECIFIC EVALUATION FOCUS:
1. **Information Architecture**: How well does the section organize information?
2. **Visual Hierarchy**: Does the section create clear visual relationships?
3. **Functional Grouping**: Are related elements properly grouped?
4. **Section Coherence**: Do all child elements serve the section's purpose?
5. **Navigation & Flow**: How does the section support user navigation?
6. **Consistency**: Are patterns consistent within the section?
7. **Accessibility**: Is the section structure accessible?
8. **Content Organization**: Is content logically organized?

RESPONSE FORMAT (JSON):
{{
    "violations": [
        {{
            "heuristic": "Heuristic Name",
            "violation": "Brief description of section-level violation",
            "reason": "Detailed explanation considering section structure and child elements",
            "severity": "high|medium|low",
            "recommendation": "Specific recommendation for section improvement",
            "affects_children": "How this violation impacts child elements"
        }}
    ],
    "passed_checks": [
        "List of heuristic names that this section passes"
    ],
    "overall_score": 85,
    "summary": "Brief assessment of section's organizational effectiveness",
    "key_recommendations": [
        "Most important section-level improvements"
    ],
    "section_strengths": [
        "What this section does well in organizing child elements"
    ]
}}

IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.
"""
    return prompt

def _create_child_evaluation_prompt(self, element_info):
    """Create evaluation prompt for child elements with section context"""

    element_json = json.dumps(element_info, indent=2)

    prompt = f"""
You are a UX expert conducting a comprehensive heuristic evaluation of a UI CHILD ELEMENT within its parent section context.

HEURISTIC EVALUATION PRINCIPLES:
{self.evaluator.heuristic_principles}

CHILD ELEMENT TO EVALUATE (with section context):
{element_json}

CHILD ELEMENT EVALUATION TASK:
Analyze this UI child element against ALL 10 Nielsen's usability heuristics, considering:
1. The element's individual usability and functionality
2. How well it fits within its parent section
3. Its relationship to sibling elements in the same section
4. Its contribution to the section's overall purpose
5. Its role in the section's information hierarchy

CHILD ELEMENT EVALUATION FOCUS:
1. **Individual Functionality**: Does the element work well on its own?
2. **Section Integration**: How well does it integrate with the parent section?
3. **Sibling Relationships**: How does it relate to other elements in the section?
4. **Contextual Appropriateness**: Is it appropriate for its section context?
5. **Hierarchy Contribution**: Does it support the section's information hierarchy?
6. **Interaction Patterns**: Are interaction patterns consistent within the section?
7. **Visual Consistency**: Is it visually consistent with section patterns?
8. **Accessibility**: Is it accessible within the section context?

RESPONSE FORMAT (JSON):
{{
    "violations": [
        {{
            "heuristic": "Heuristic Name",
            "violation": "Brief description of child element violation",
            "reason": "Detailed explanation considering section context",
            "severity": "high|medium|low",
            "recommendation": "Specific recommendation for child element improvement",
            "section_impact": "How this violation affects the parent section"
        }}
    ],
    "passed_checks": [
        "List of heuristic names that this child element passes"
    ],
    "overall_score": 85,
    "summary": "Brief assessment of child element's effectiveness within section",
    "key_recommendations": [
        "Most important child element improvements"
    ],
    "section_integration": "How well this element integrates with its parent section"
}}

IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.
"""
    return prompt

# Bind prompt methods to the SectionWiseHeuristicEvaluationSystem class
SectionWiseHeuristicEvaluationSystem._create_section_evaluation_prompt = _create_section_evaluation_prompt
SectionWiseHeuristicEvaluationSystem._create_child_evaluation_prompt = _create_child_evaluation_prompt

print("✅ Section-wise evaluation prompt methods added!")

# Add main evaluation and reporting methods for section-wise system
def perform_section_wise_evaluation(self, show_visual=True):
    """Perform comprehensive section-wise heuristic evaluation"""

    if not self.sections:
        print("❌ No sections found. Please load hierarchical data first.")
        return False

    print("\n🚀 Starting Section-wise Heuristic Evaluation...")
    print("=" * 70)
    print(f"📋 Evaluating {len(self.sections)} sections with their child elements")

    self.section_evaluations = []

    for i, section in enumerate(self.sections):
        section_label = section['data'].get('label', 'Unknown Section')
        print(f"\n📋 Section {i+1}/{len(self.sections)}: {section_label}")
        print("-" * 50)

        try:
            # Evaluate section with all its children
            section_evaluation = self.evaluate_section_with_children(section, show_visual)
            self.section_evaluations.append(section_evaluation)

            print(f"✅ Section '{section_label}' evaluation completed")

        except Exception as e:
            print(f"❌ Error evaluating section '{section_label}': {str(e)}")
            continue

    print(f"\n🎉 Section-wise heuristic evaluation completed!")
    print(f"📊 Evaluated {len(self.section_evaluations)} sections")

    # Calculate overall statistics
    total_child_evaluations = sum(len(se['child_evaluations']) for se in self.section_evaluations)
    print(f"🔍 Total child elements evaluated: {total_child_evaluations}")

    return True

def generate_section_wise_report(self):
    """Generate comprehensive section-wise heuristic evaluation report"""

    if not self.section_evaluations:
        print("❌ No section evaluation results available. Please run evaluation first.")
        return None

    print("\n📋 SECTION-WISE HEURISTIC EVALUATION REPORT")
    print("=" * 80)

    # Overall statistics
    total_sections = len(self.section_evaluations)
    total_children = sum(se['total_children'] for se in self.section_evaluations)
    total_violations = sum(se['total_violations'] for se in self.section_evaluations)

    avg_section_score = sum(se['section_score'] for se in self.section_evaluations) / total_sections if total_sections > 0 else 0
    avg_child_score = sum(se['average_child_score'] for se in self.section_evaluations) / total_sections if total_sections > 0 else 0

    print(f"\n📊 OVERALL STATISTICS:")
    print(f"   • Sections Evaluated: {total_sections}")
    print(f"   • Total Child Elements: {total_children}")
    print(f"   • Total Violations Found: {total_violations}")
    print(f"   • Average Section Score: {avg_section_score:.1f}/100")
    print(f"   • Average Child Elements Score: {avg_child_score:.1f}/100")

    # Section-by-section analysis
    print(f"\n🏗️ SECTION-BY-SECTION ANALYSIS:")
    print("-" * 80)

    for i, section_eval in enumerate(self.section_evaluations):
        section_info = section_eval['section_info']
        section_label = section_info['data'].get('label', 'Unknown Section')
        section_score = section_eval['section_score']
        child_score = section_eval['average_child_score']
        total_violations = section_eval['total_violations']
        child_count = section_eval['total_children']

        print(f"\n{i+1}. 📋 {section_label}")
        print(f"   📊 Section Score: {section_score}/100")
        print(f"   👥 Child Elements: {child_count} (Avg Score: {child_score:.1f}/100)")
        print(f"   🚨 Total Violations: {total_violations}")

        # Section violations
        section_violations = section_eval['section_evaluation'].get('violations', [])
        if section_violations:
            print(f"   ❌ Section-level Issues ({len(section_violations)}):")
            for violation in section_violations[:3]:  # Show top 3
                heuristic = violation.get('heuristic', 'Unknown')
                severity = violation.get('severity', 'medium')
                description = violation.get('violation', 'No description')
                print(f"      • [{severity.upper()}] {heuristic}: {description}")

        # Child element summary
        child_violations = [v for ce in section_eval['child_evaluations'] for v in ce.get('violations', [])]
        if child_violations:
            print(f"   ⚠️ Child Element Issues ({len(child_violations)}):")
            # Group by severity
            high_severity = [v for v in child_violations if v.get('severity', '').lower() == 'high']
            medium_severity = [v for v in child_violations if v.get('severity', '').lower() == 'medium']
            low_severity = [v for v in child_violations if v.get('severity', '').lower() == 'low']

            print(f"      • High: {len(high_severity)}, Medium: {len(medium_severity)}, Low: {len(low_severity)}")

    # Top recommendations across all sections
    all_recommendations = []
    for section_eval in self.section_evaluations:
        all_recommendations.extend(section_eval['section_evaluation'].get('recommendations', []))
        for child_eval in section_eval['child_evaluations']:
            all_recommendations.extend(child_eval.get('recommendations', []))

    if all_recommendations:
        print(f"\n💡 TOP RECOMMENDATIONS:")
        print("-" * 80)
        for i, rec in enumerate(all_recommendations[:10], 1):  # Top 10
            print(f"{i}. {rec}")

    print("\n" + "=" * 80)
    print("📋 End of Section-wise Heuristic Evaluation Report")

    return {
        'total_sections': total_sections,
        'total_children': total_children,
        'total_violations': total_violations,
        'average_section_score': avg_section_score,
        'average_child_score': avg_child_score,
        'section_evaluations': self.section_evaluations
    }

# Enhanced visual evaluation method
def perform_visual_enhanced_section_wise_evaluation(self, show_visual=True):
    """Perform comprehensive visual-enhanced section-wise heuristic evaluation"""

    if not self.sections:
        print("❌ No sections found. Please load hierarchical data first.")
        return False

    print("\n🚀 Starting Visual-Enhanced Section-wise Heuristic Evaluation...")
    print("=" * 80)
    print(f"📋 Evaluating {len(self.sections)} sections with their child elements")

    # Check visual analysis availability
    has_visual = show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None

    if has_visual:
        print("🖼️ Visual analysis enabled - Sections and children will be highlighted and analyzed visually")
    else:
        print("📊 Visual analysis not available - Using JSON data only")

    print("\n🎯 Enhanced Evaluation Features:")
    print("   • Section-wise visual closeups and analysis")
    print("   • Child element visual highlighting when needed")
    print("   • Intelligent decision-making for individual vs. section-based evaluation")
    print("   • Combined visual and JSON data analysis")

    self.section_evaluations = []

    for i, section in enumerate(self.sections):
        section_label = section['data'].get('label', 'Unknown Section')
        print(f"\n📋 Section {i+1}/{len(self.sections)}: {section_label}")
        print("-" * 60)

        try:
            # Use visual-enhanced evaluation method
            section_evaluation = self.evaluate_section_with_children_visual(section, has_visual)
            self.section_evaluations.append(section_evaluation)

            print(f"✅ Section '{section_label}' evaluation completed")

        except Exception as e:
            print(f"❌ Error evaluating section '{section_label}': {str(e)}")
            continue

    print(f"\n🎉 Visual-enhanced section-wise heuristic evaluation completed!")
    print(f"📊 Evaluated {len(self.section_evaluations)} sections")

    # Calculate overall statistics
    total_child_evaluations = sum(len(se['child_evaluations']) for se in self.section_evaluations)
    individual_evaluations = sum(1 for se in self.section_evaluations if se.get('evaluation_strategy') == 'individual_children')
    section_based_evaluations = len(self.section_evaluations) - individual_evaluations

    print(f"🔍 Total child elements evaluated: {total_child_evaluations}")
    print(f"🎯 Individual child evaluations: {individual_evaluations} sections")
    print(f"📋 Section-based evaluations: {section_based_evaluations} sections")
    print(f"🖼️ Visual analysis used: {'Yes' if has_visual else 'No'}")

    return True

# Bind methods to the SectionWiseHeuristicEvaluationSystem class
SectionWiseHeuristicEvaluationSystem.perform_section_wise_evaluation = perform_section_wise_evaluation
SectionWiseHeuristicEvaluationSystem.perform_visual_enhanced_section_wise_evaluation = perform_visual_enhanced_section_wise_evaluation
SectionWiseHeuristicEvaluationSystem.generate_section_wise_report = generate_section_wise_report

print("✅ Section-wise main evaluation and reporting methods added!")

# Two-Level Visual-Enhanced Evaluation Method
def perform_two_level_visual_enhanced_evaluation(self, show_visual=True):
    """Perform comprehensive two-level visual-enhanced heuristic evaluation"""

    if not self.sections:
        print("❌ No sections found. Please load hierarchical data first.")
        return False

    print("\n🚀 Starting Two-Level Visual-Enhanced Heuristic Evaluation...")
    print("=" * 80)
    print(f"📋 Evaluating {len(self.sections)} sections using two-level approach")

    # Check visual analysis availability
    has_visual = show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None

    if has_visual:
        print("🖼️ Visual analysis enabled - Sections and immediate children will be highlighted and analyzed visually")
    else:
        print("📊 Visual analysis not available - Using JSON data only")

    print("\n🎯 Two-Level Evaluation Approach:")
    print("   📋 Level 0: Evaluate each section as an organizational unit")
    print("   🔍 Level 1: Evaluate immediate children individually")
    print("   📝 Level 2+: Use nested children as context only (not evaluated individually)")
    print("   🖼️ Visual closeups and analysis for both levels")
    print("   📊 Combined visual and JSON data analysis")

    self.section_evaluations = []

    for i, section in enumerate(self.sections):
        section_label = section['data'].get('label', 'Unknown Section')
        print(f"\n📋 Section {i+1}/{len(self.sections)}: {section_label}")
        print("=" * 70)

        try:
            # Use two-level evaluation method
            section_evaluation = self.evaluate_section_two_level(section, has_visual)
            self.section_evaluations.append(section_evaluation)

            print(f"✅ Two-level evaluation for '{section_label}' completed")

        except Exception as e:
            print(f"❌ Error in two-level evaluation for section '{section_label}': {str(e)}")
            continue

    print(f"\n🎉 Two-Level visual-enhanced heuristic evaluation completed!")
    print(f"📊 Evaluated {len(self.section_evaluations)} sections")

    # Calculate two-level statistics
    total_level1_evaluations = sum(se['total_level1_children'] for se in self.section_evaluations)
    total_context_children = sum(se['total_context_children'] for se in self.section_evaluations)
    avg_level0_score = sum(se['level0_score'] for se in self.section_evaluations) / len(self.section_evaluations) if self.section_evaluations else 0
    avg_level1_score = sum(se['average_level1_score'] for se in self.section_evaluations) / len(self.section_evaluations) if self.section_evaluations else 0

    print(f"\n📊 Two-Level Evaluation Statistics:")
    print(f"   📋 Level 0 (Sections): {len(self.section_evaluations)} evaluated")
    print(f"   🔍 Level 1 (Immediate Children): {total_level1_evaluations} evaluated")
    print(f"   📝 Level 2+ (Context Children): {total_context_children} used for context")
    print(f"   📊 Average Level 0 Score: {avg_level0_score:.1f}/100")
    print(f"   📊 Average Level 1 Score: {avg_level1_score:.1f}/100")
    print(f"   🖼️ Visual analysis used: {'Yes' if has_visual else 'No'}")

    return True

# Bind the two-level method to the class
SectionWiseHeuristicEvaluationSystem.perform_two_level_visual_enhanced_evaluation = perform_two_level_visual_enhanced_evaluation

print("✅ Two-Level visual-enhanced evaluation method added!")

# Two-Level Compatible Reporting Method
def generate_two_level_report(self):
    """Generate comprehensive two-level heuristic evaluation report"""

    if not self.section_evaluations:
        print("❌ No evaluation results available. Please run evaluation first.")
        return None

    # Detect evaluation type
    is_two_level = any('evaluation_approach' in se and se['evaluation_approach'] == 'two_level' for se in self.section_evaluations)

    if is_two_level:
        print("\n📋 TWO-LEVEL HEURISTIC EVALUATION REPORT")
        print("=" * 80)

        # Two-level statistics
        total_sections = len(self.section_evaluations)
        total_level1_children = sum(se.get('total_level1_children', 0) for se in self.section_evaluations)
        total_context_children = sum(se.get('total_context_children', 0) for se in self.section_evaluations)
        total_violations = sum(se.get('total_violations', 0) for se in self.section_evaluations)

        avg_level0_score = sum(se.get('level0_score', 0) for se in self.section_evaluations) / total_sections if total_sections > 0 else 0
        avg_level1_score = sum(se.get('average_level1_score', 0) for se in self.section_evaluations) / total_sections if total_sections > 0 else 0

        print(f"\n📊 TWO-LEVEL EVALUATION STATISTICS:")
        print(f"   📋 Level 0 (Sections): {total_sections} evaluated")
        print(f"   🔍 Level 1 (Immediate Children): {total_level1_children} evaluated")
        print(f"   📝 Level 2+ (Context Children): {total_context_children} used for context")
        print(f"   📊 Average Level 0 Score: {avg_level0_score:.1f}/100")
        print(f"   📊 Average Level 1 Score: {avg_level1_score:.1f}/100")
        print(f"   🚨 Total Violations Found: {total_violations}")

        # Section-by-section two-level analysis
        print(f"\n🏗️ SECTION-BY-SECTION TWO-LEVEL ANALYSIS:")
        print("-" * 80)

        for i, section_eval in enumerate(self.section_evaluations):
            section_info = section_eval['section_info']
            section_label = section_info['data'].get('label', 'Unknown Section')
            level0_score = section_eval.get('level0_score', 0)
            level1_score = section_eval.get('average_level1_score', 0)
            total_violations = section_eval.get('total_violations', 0)
            level1_count = section_eval.get('total_level1_children', 0)
            context_count = section_eval.get('total_context_children', 0)

            print(f"\n{i+1}. 📋 {section_label}")
            print(f"   📋 Level 0 (Section) Score: {level0_score}/100")
            print(f"   🔍 Level 1 (Immediate Children): {level1_count} elements (Avg Score: {level1_score:.1f}/100)")
            print(f"   📝 Level 2+ (Context): {context_count} nested children")
            print(f"   🚨 Total Violations: {total_violations}")

            # Level 0 violations
            level0_evaluation = section_eval.get('level0_evaluation', {})
            level0_violations = level0_evaluation.get('violations', [])
            if level0_violations:
                print(f"   ❌ Level 0 Issues ({len(level0_violations)}):")
                for violation in level0_violations[:3]:  # Show top 3
                    heuristic = violation.get('heuristic', 'Unknown')
                    severity = violation.get('severity', 'medium')
                    description = violation.get('violation', 'No description')
                    print(f"      • [{severity.upper()}] {heuristic}: {description}")

            # Level 1 violations summary
            level1_evaluations = section_eval.get('level1_evaluations', [])
            level1_violations = [v for le in level1_evaluations for v in le.get('violations', [])]
            if level1_violations:
                print(f"   ⚠️ Level 1 Issues ({len(level1_violations)}):")
                # Group by severity
                high_severity = [v for v in level1_violations if v.get('severity', '').lower() == 'high']
                medium_severity = [v for v in level1_violations if v.get('severity', '').lower() == 'medium']
                low_severity = [v for v in level1_violations if v.get('severity', '').lower() == 'low']

                print(f"      • High: {len(high_severity)}, Medium: {len(medium_severity)}, Low: {len(low_severity)}")

        # Top recommendations across all levels
        all_recommendations = []
        for section_eval in self.section_evaluations:
            # Level 0 recommendations
            level0_eval = section_eval.get('level0_evaluation', {})
            all_recommendations.extend(level0_eval.get('recommendations', []))
            all_recommendations.extend(level0_eval.get('key_recommendations', []))

            # Level 1 recommendations
            for level1_eval in section_eval.get('level1_evaluations', []):
                all_recommendations.extend(level1_eval.get('recommendations', []))
                all_recommendations.extend(level1_eval.get('key_recommendations', []))

        if all_recommendations:
            print(f"\n💡 TOP RECOMMENDATIONS (All Levels):")
            print("-" * 80)
            for i, rec in enumerate(all_recommendations[:15], 1):  # Top 15
                print(f"{i}. {rec}")

        print("\n" + "=" * 80)
        print("📋 End of Two-Level Heuristic Evaluation Report")

        return {
            'evaluation_type': 'two_level',
            'total_sections': total_sections,
            'total_level1_children': total_level1_children,
            'total_context_children': total_context_children,
            'total_violations': total_violations,
            'average_level0_score': avg_level0_score,
            'average_level1_score': avg_level1_score,
            'section_evaluations': self.section_evaluations
        }

    else:
        # Fall back to legacy reporting for non-two-level evaluations
        return self.generate_section_wise_report()

# Enhanced generate_section_wise_report to handle both types
def generate_compatible_report(self):
    """Generate report compatible with both legacy and two-level evaluations"""

    if not self.section_evaluations:
        print("❌ No evaluation results available. Please run evaluation first.")
        return None

    # Check if this is a two-level evaluation
    is_two_level = any('evaluation_approach' in se and se['evaluation_approach'] == 'two_level' for se in self.section_evaluations)

    if is_two_level:
        return self.generate_two_level_report()
    else:
        return self.generate_section_wise_report()

# Enhanced detailed reporting function with visual representations
def generate_enhanced_detailed_report(self, show_visual=True, max_violations_per_element=5):
    """Generate enhanced detailed section-wise evaluation report with visual representations"""

    if not self.section_evaluations:
        print("❌ No section evaluation results available. Please run evaluation first.")
        return None

    # Check if visual analysis is available
    has_visual = show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None

    # Calculate statistics
    total_sections = len(self.section_evaluations)
    total_violations = sum(se.get('total_violations', 0) for se in self.section_evaluations)

    # Determine evaluation type
    is_two_level = any('evaluation_approach' in se and se['evaluation_approach'] == 'two_level' for se in self.section_evaluations)

    # Generate enhanced report header
    print("=" * 100)
    print("🔍 ENHANCED DETAILED HEURISTIC EVALUATION REPORT")
    print("=" * 100)
    print(f"📊 Report Configuration:")
    print(f"   • Visual Analysis: {'✅ Enabled' if has_visual else '❌ Disabled'}")
    print(f"   • Evaluation Type: {'Two-Level' if is_two_level else 'Standard Section-wise'}")
    print(f"   • Max Violations per Element: {max_violations_per_element}")
    print(f"   • Total Sections: {total_sections}")
    print(f"   • Total Violations: {total_violations}")

    # Detailed section-by-section analysis with visual representations
    print(f"\n🏗️ DETAILED SECTION-BY-SECTION ANALYSIS:")
    print("=" * 100)

    for section_idx, section_eval in enumerate(self.section_evaluations):
        section_info = section_eval['section_info']
        section_data = section_info['data']
        section_label = section_data.get('label', 'Unknown Section')
        section_key = section_info['key']

        print(f"\n{'='*20} SECTION {section_idx + 1}: {section_label} {'='*20}")

        # Show section visual if available
        if has_visual:
            try:
                print(f"\n🖼️ SECTION VISUAL REPRESENTATION:")
                section_coords = section_data.get('coordinates', {})
                if section_coords:
                    highlighted, cropped = self.evaluator.visual_analyzer.display_element_analysis(
                        section_coords, f"Section: {section_label}"
                    )
                    print(f"   📍 Location: x={section_coords.get('x', 0)}, y={section_coords.get('y', 0)}")
                    print(f"   📏 Size: {section_coords.get('width', 0)}x{section_coords.get('height', 0)} pixels")
                else:
                    print("   ⚠️ No coordinate data available for visual representation")
            except Exception as e:
                print(f"   ❌ Could not display section visual: {str(e)}")

        # Section-level evaluation details
        if is_two_level:
            level0_evaluation = section_eval.get('level0_evaluation', {})
            level0_score = section_eval.get('level0_score', 0)
            level0_violations = level0_evaluation.get('violations', [])

            print(f"\n📋 LEVEL 0 (SECTION) EVALUATION:")
            print(f"   🎯 Score: {level0_score}/100")
            print(f"   🚨 Violations: {len(level0_violations)}")

            if level0_violations:
                print(f"\n   ❌ SECTION-LEVEL HEURISTIC VIOLATIONS:")
                for i, violation in enumerate(level0_violations[:max_violations_per_element], 1):
                    heuristic = violation.get('heuristic', 'Unknown Heuristic')
                    severity = violation.get('severity', 'medium').upper()
                    violation_desc = violation.get('violation', 'No description provided')
                    reason = violation.get('reason', 'No reason provided')

                    print(f"      {i}. 🔴 [{severity}] {heuristic}")
                    print(f"         📝 Issue: {violation_desc}")
                    print(f"         💡 Reason: {reason}")
                    print()

            # Level 1 children evaluation details
            level1_evaluations = section_eval.get('level1_evaluations', [])
            level1_count = section_eval.get('total_level1_children', 0)
            level1_score = section_eval.get('average_level1_score', 0)

            print(f"\n🔍 LEVEL 1 (IMMEDIATE CHILDREN) EVALUATION:")
            print(f"   👥 Children Count: {level1_count}")
            print(f"   🎯 Average Score: {level1_score:.1f}/100")

            if level1_evaluations:
                print(f"\n   📋 INDIVIDUAL CHILD ELEMENT DETAILS:")
                for child_idx, child_eval in enumerate(level1_evaluations):
                    child_info = child_eval.get('element_info', {})
                    child_label = child_info.get('label', f'Child {child_idx + 1}')
                    child_score = child_eval.get('overall_score', 0)
                    child_violations = child_eval.get('violations', [])

                    print(f"\n      🔸 Child {child_idx + 1}: {child_label}")
                    print(f"         🎯 Score: {child_score}/100")
                    print(f"         🚨 Violations: {len(child_violations)}")

                    # Show child visual if available
                    if has_visual and child_info.get('coordinates'):
                        try:
                            print(f"         🖼️ Visual Representation:")
                            child_coords = child_info['coordinates']
                            child_highlighted, child_cropped = self.evaluator.visual_analyzer.display_element_analysis(
                                child_coords, f"Child: {child_label}"
                            )
                            print(f"            📍 Location: x={child_coords.get('x', 0)}, y={child_coords.get('y', 0)}")
                            print(f"            📏 Size: {child_coords.get('width', 0)}x{child_coords.get('height', 0)} pixels")
                        except Exception as e:
                            print(f"            ❌ Could not display child visual: {str(e)}")

                    # Show child violations in detail
                    if child_violations:
                        print(f"         ❌ HEURISTIC VIOLATIONS:")
                        for v_idx, violation in enumerate(child_violations[:max_violations_per_element], 1):
                            heuristic = violation.get('heuristic', 'Unknown Heuristic')
                            severity = violation.get('severity', 'medium').upper()
                            violation_desc = violation.get('violation', 'No description provided')
                            reason = violation.get('reason', 'No reason provided')

                            print(f"            {v_idx}. 🔴 [{severity}] {heuristic}")
                            print(f"               📝 Issue: {violation_desc}")
                            print(f"               💡 Reason: {reason}")
                    else:
                        print(f"         ✅ No violations found for this child element")

        else:
            # Standard section evaluation
            section_evaluation = section_eval.get('section_evaluation', {})
            section_score = section_eval.get('section_score', 0)
            section_violations = section_evaluation.get('violations', [])

            print(f"\n📋 SECTION EVALUATION:")
            print(f"   🎯 Score: {section_score}/100")
            print(f"   🚨 Violations: {len(section_violations)}")

            if section_violations:
                print(f"\n   ❌ SECTION HEURISTIC VIOLATIONS:")
                for i, violation in enumerate(section_violations[:max_violations_per_element], 1):
                    heuristic = violation.get('heuristic', 'Unknown Heuristic')
                    severity = violation.get('severity', 'medium').upper()
                    violation_desc = violation.get('violation', 'No description provided')
                    reason = violation.get('reason', 'No reason provided')

                    print(f"      {i}. 🔴 [{severity}] {heuristic}")
                    print(f"         📝 Issue: {violation_desc}")
                    print(f"         💡 Reason: {reason}")
                    print()

            # Child elements evaluation details
            child_evaluations = section_eval.get('child_evaluations', [])
            if child_evaluations:
                print(f"\n   👥 CHILD ELEMENTS EVALUATION:")
                print(f"      📊 Total Children: {len(child_evaluations)}")

                for child_idx, child_eval in enumerate(child_evaluations):
                    child_info = child_eval.get('element_info', {})
                    child_label = child_info.get('label', f'Child {child_idx + 1}')
                    child_score = child_eval.get('overall_score', 0)
                    child_violations = child_eval.get('violations', [])

                    print(f"\n      🔸 Child {child_idx + 1}: {child_label}")
                    print(f"         🎯 Score: {child_score}/100")
                    print(f"         🚨 Violations: {len(child_violations)}")

                    # Show child visual if available
                    if has_visual and child_info.get('coordinates'):
                        try:
                            print(f"         🖼️ Visual Representation:")
                            child_coords = child_info['coordinates']
                            child_highlighted, child_cropped = self.evaluator.visual_analyzer.display_element_analysis(
                                child_coords, f"Child: {child_label}"
                            )
                            print(f"            📍 Location: x={child_coords.get('x', 0)}, y={child_coords.get('y', 0)}")
                            print(f"            📏 Size: {child_coords.get('width', 0)}x{child_coords.get('height', 0)} pixels")
                        except Exception as e:
                            print(f"            ❌ Could not display child visual: {str(e)}")

                    # Show child violations in detail
                    if child_violations:
                        print(f"         ❌ HEURISTIC VIOLATIONS:")
                        for v_idx, violation in enumerate(child_violations[:max_violations_per_element], 1):
                            heuristic = violation.get('heuristic', 'Unknown Heuristic')
                            severity = violation.get('severity', 'medium').upper()
                            violation_desc = violation.get('violation', 'No description provided')
                            reason = violation.get('reason', 'No reason provided')

                            print(f"            {v_idx}. 🔴 [{severity}] {heuristic}")
                            print(f"               📝 Issue: {violation_desc}")
                            print(f"               💡 Reason: {reason}")
                    else:
                        print(f"         ✅ No violations found for this child element")

        print(f"\n{'='*80}")

    return {
        'report_type': 'enhanced_detailed',
        'evaluation_type': 'two_level' if is_two_level else 'standard',
        'has_visual_analysis': has_visual,
        'total_sections': total_sections,
        'total_violations': total_violations,
        'section_evaluations': self.section_evaluations
    }

# Bind the new reporting methods to the class
SectionWiseHeuristicEvaluationSystem.generate_two_level_report = generate_two_level_report
SectionWiseHeuristicEvaluationSystem.generate_compatible_report = generate_compatible_report
SectionWiseHeuristicEvaluationSystem.generate_enhanced_detailed_report = generate_enhanced_detailed_report

print("✅ Enhanced detailed reporting methods added!")

# Initialize the Enhanced Section-wise Heuristic Evaluation System
print("🚀 Initializing Section-wise Heuristic Evaluation System")
print("=" * 70)

# Create section-wise system instance
section_wise_system = SectionWiseHeuristicEvaluationSystem()

# Enhance evaluator with visual analysis capabilities
section_wise_system.evaluator.enhance_with_visual_analysis()

# Load hierarchical data with two-level structure from configuration files or fallback
section_wise_system.load_two_level_hierarchical_data()

print("\n🎉 Two-Level section-wise system ready for hierarchical heuristic evaluation!")
print("\n📋 Available sections for two-level evaluation:")
for i, section in enumerate(section_wise_system.sections):
    section_label = section['data'].get('label', 'Unknown Section')
    immediate_count = len(get_immediate_children_for_evaluation(section))
    context_count = len(get_nested_children_context(section))
    x = section['data'].get('coordinates', {}).get('x', 0)
    y = section['data'].get('coordinates', {}).get('y', 0)
    print(f"  {i+1}. {section_label} at ({x}, {y})")
    print(f"     📋 Level 0: Section evaluation")
    print(f"     🔍 Level 1: {immediate_count} immediate children (evaluated individually)")
    print(f"     📝 Level 2+: {context_count} nested children (context only)")

print("\n💡 Two-Level Evaluation System:")
print("   📋 Level 0: Each section evaluated as an organizational unit")
print("   🔍 Level 1: Immediate children evaluated individually with section context")
print("   📝 Level 2+: Nested children used as context to improve evaluation accuracy")

# Load screenshot for visual-enhanced heuristic evaluation
print("🖼️ Loading Screenshot for Visual Analysis")
print("=" * 50)

# Try to load screenshot from configuration path
screenshot_loaded = section_wise_system.evaluator.load_screenshot_for_evaluation()

if screenshot_loaded:
    print("\n✅ Screenshot loaded successfully!")
    print("🎯 Visual-enhanced heuristic evaluation is now available.")
    print("📊 Elements will be highlighted and cropped during evaluation.")
else:
    print("\n⚠️ Screenshot not found at default path.")
    print("💡 You can upload a screenshot using the interface below.")
    print("🔄 Evaluation will proceed with technical data only.")

# Create screenshot upload interface
def upload_screenshot():
    """Upload screenshot for visual analysis"""
    print("\n📁 Upload Screenshot Image")
    print("=" * 30)
    print("Please upload a screenshot image (PNG, JPG, etc.) of the web page.")

    # Upload screenshot file
    uploaded = files.upload()

    if uploaded:
        try:
            # Get the uploaded filename
            screenshot_filename = list(uploaded.keys())[0]

            # Load the screenshot
            success = heuristic_system.evaluator.load_screenshot_for_evaluation(screenshot_filename)

            if success:
                print(f"\n✅ Screenshot '{screenshot_filename}' loaded successfully!")
                print("🎯 Visual-enhanced evaluation is now ready.")

                # Display a preview of the screenshot
                screenshot = heuristic_system.evaluator.visual_analyzer.screenshot
                plt.figure(figsize=(12, 8))
                plt.imshow(screenshot)
                plt.title(f"Loaded Screenshot: {screenshot_filename}")
                plt.axis('off')
                plt.show()

                return True
            else:
                print(f"\n❌ Failed to load screenshot '{screenshot_filename}'")
                return False

        except Exception as e:
            print(f"\n❌ Error processing screenshot: {str(e)}")
            return False
    else:
        print("\n⚠️ No file uploaded.")
        return False

# Create upload button for screenshot
screenshot_button = widgets.Button(
    description='🖼️ Upload Screenshot',
    button_style='success',
    layout=widgets.Layout(width='200px')
)

screenshot_output = widgets.Output()

def on_screenshot_click(button):
    with screenshot_output:
        clear_output(wait=True)
        upload_screenshot()

screenshot_button.on_click(on_screenshot_click)

display(widgets.VBox([
    widgets.HTML("<h3>🖼️ Screenshot Upload</h3>"),
    widgets.HTML("<p>Upload a screenshot to enable visual-enhanced heuristic evaluation.</p>"),
    screenshot_button,
    screenshot_output
]))

# Perform comprehensive section-wise heuristic evaluation
print("🔍 Starting Section-wise Heuristic Evaluation")
print("=" * 70)
print("This will evaluate each section and its child elements against Nielsen's 10 usability heuristics using Gemini AI.")

# Check if visual analysis is available
has_visual = hasattr(section_wise_system.evaluator, 'visual_analyzer') and section_wise_system.evaluator.visual_analyzer.screenshot is not None

if has_visual:
    print("🖼️ Visual analysis enabled - Sections and elements will be highlighted and analyzed visually.")
else:
    print("📊 Visual analysis not available - Using technical data only.")

print("\n🏗️ Section-wise evaluation approach:")
print("   1. Evaluate each section as an organizational unit")
print("   2. Evaluate child elements within their section context")
print("   3. Consider hierarchical relationships and dependencies")
print("   4. Generate comprehensive section-wise reports")

print("\nPlease wait while the section-wise evaluation is performed...\n")

# Run the two-level visual-enhanced evaluation
success = section_wise_system.perform_two_level_visual_enhanced_evaluation(show_visual=has_visual)

if success:
    print("\n✅ Section-wise heuristic evaluation completed successfully!")
    print("📋 Generating comprehensive section-wise report...")
else:
    print("\n❌ Section-wise heuristic evaluation failed. Please check the data and try again.")

# Generate and display comprehensive two-level heuristic evaluation report
report = section_wise_system.generate_two_level_report()

if report:
    if report.get('evaluation_type') == 'two_level':
        print("\n🎯 TWO-LEVEL EVALUATION SUMMARY:")
        print(f"Average Level 0 (Section) Score: {report['average_level0_score']:.1f}/100")
        print(f"Average Level 1 (Immediate Children) Score: {report['average_level1_score']:.1f}/100")

        # Overall assessment based on combined scores
        combined_score = (report['average_level0_score'] + report['average_level1_score']) / 2

        if combined_score >= 80:
            print("🟢 Excellent two-level organization and usability - Minor improvements needed")
        elif combined_score >= 60:
            print("🟡 Good two-level structure with some issues - Improvements recommended")
        elif combined_score >= 40:
            print("🟠 Fair two-level organization - Significant improvements needed")
        else:
            print("🔴 Poor two-level structure and usability - Major improvements required")

        print(f"\n📊 Two-Level Detailed Statistics:")
        print(f"   📋 Level 0 (Sections): {report['total_sections']} evaluated")
        print(f"   🔍 Level 1 (Immediate Children): {report['total_level1_children']} evaluated")
        print(f"   📝 Level 2+ (Context Children): {report['total_context_children']} used for context")
        print(f"   🚨 Total Violations Found: {report['total_violations']}")
        print(f"   📊 Average Level 1 Children per Section: {report['total_level1_children'] / report['total_sections']:.1f}")
        print(f"   📝 Average Context Children per Section: {report['total_context_children'] / report['total_sections']:.1f}")
    else:
        # Legacy report format
        print("\n🎯 SECTION-WISE EVALUATION SUMMARY:")
        print(f"Average Section Score: {report.get('average_section_score', 0):.1f}/100")
        print(f"Average Child Elements Score: {report.get('average_child_score', 0):.1f}/100")

        print(f"\n📊 Detailed Statistics:")
        print(f"   • Total Sections Evaluated: {report.get('total_sections', 0)}")
        print(f"   • Total Child Elements: {report.get('total_children', 0)}")
        print(f"   • Total Violations Found: {report.get('total_violations', 0)}")

else:
    print("❌ Could not generate evaluation report. Please run evaluation first.")

# Generate Enhanced Detailed Section-wise Evaluation Report
print("🔍 Generating Enhanced Detailed Evaluation Report")
print("=" * 70)
print("This report provides:")
print("   • Detailed heuristic violation descriptions with reasons")
print("   • Visual representations of each section and element")
print("   • Element coordinates and positioning information")
print("   • Comprehensive analysis for easy identification of issues")
print("\nGenerating report...\n")

# Generate the enhanced detailed report
enhanced_report = section_wise_system.generate_enhanced_detailed_report(
    show_visual=True,  # Enable visual representations
    max_violations_per_element=5  # Show up to 5 violations per element
)

if enhanced_report:
    print("\n🎯 ENHANCED REPORT SUMMARY:")
    print(f"Report Type: {enhanced_report['report_type']}")
    print(f"Evaluation Type: {enhanced_report['evaluation_type']}")
    print(f"Visual Analysis: {'✅ Enabled' if enhanced_report['has_visual_analysis'] else '❌ Disabled'}")
    print(f"Total Sections Analyzed: {enhanced_report['total_sections']}")
    print(f"Total Violations Found: {enhanced_report['total_violations']}")

    if enhanced_report['has_visual_analysis']:
        print("\n🖼️ VISUAL FEATURES INCLUDED:")
        print("   📸 Section screenshots with highlighted boundaries")
        print("   🔍 Element close-ups for detailed analysis")
        print("   📍 Precise coordinate information for each element")
        print("   📏 Size and positioning data for visual context")

    print("\n💡 REPORT BENEFITS:")
    print("   🔍 Easy identification of problematic elements")
    print("   📝 Detailed violation descriptions with explanations")
    print("   🎯 Clear reasoning for each heuristic violation")
    print("   🖼️ Visual context for better understanding")
    print("   📊 Comprehensive section-by-section breakdown")

else:
    print("❌ Could not generate enhanced detailed report. Please run evaluation first.")

print("\n" + "=" * 70)
print("📋 Enhanced Detailed Report Generation Complete")

# 🖼️ VISION-ENHANCED EVALUATION DEMONSTRATION
print("🚀 Vision-Enhanced Heuristic Evaluation Demo")
print("=" * 60)

# Check if system is ready
if 'section_wise_system' in locals():
    evaluator = section_wise_system.evaluator

    # Configure vision settings
    print("\n🔧 Configuring Vision Settings:")
    evaluator.configure_vision(
        enable_vision=True,
        image_quality="high",
        max_image_size=(1024, 1024)
    )

    # Load screenshot
    print("\n📸 Loading Screenshot:")
    screenshot_loaded = evaluator.load_screenshot_for_evaluation()

    if screenshot_loaded:
        print("\n🎯 Running Vision-Enhanced Evaluation on Sample Element:")

        # Get a sample element for demonstration
        if hasattr(section_wise_system, 'coordinates') and section_wise_system.coordinates:
            sample_coord = section_wise_system.coordinates[0]
            sample_element = section_wise_system.element_info.get(str(sample_coord['index']), {})

            print(f"📋 Evaluating: {sample_coord.get('label', 'Unknown Element')}")
            print(f"🔍 Index: {sample_coord.get('index', 'N/A')}")

            # Run vision-enhanced evaluation
            result = evaluator.evaluate_element_with_visual(
                sample_element,
                sample_coord,
                show_visual=True
            )

            # Display results
            print("\n📊 VISION-ENHANCED EVALUATION RESULTS:")
            print("=" * 50)
            print(f"🎯 Overall Score: {result.get('overall_score', 0)}/100")
            print(f"🖼️ Vision Enabled: {result.get('vision_enabled', False)}")
            print(f"📸 Images Sent to Gemini: {result.get('images_sent_to_gemini', False)}")
            print(f"🔍 Has Visual Context: {result.get('has_visual_context', False)}")

            if result.get('images_sent_to_gemini', False):
                print("\n✅ SUCCESS: Images were successfully sent to Gemini for analysis!")
                print("🎉 The model now has visual clarity about the UI element!")

                # Show image analysis if available
                if 'image_analysis' in result:
                    print(f"\n🖼️ Image Analysis: {result['image_analysis']}")

                # Show visual assessment
                if 'visual_assessment' in result:
                    print(f"\n👁️ Visual Assessment: {result['visual_assessment']}")
            else:
                print("\n⚠️ Images were not sent to Gemini. Check vision configuration.")

            print("\n🎊 Vision-enhanced evaluation completed successfully!")
            print("📈 The Gemini model now has comprehensive visual understanding!")
        else:
            print("❌ No sample elements available for demonstration")
    else:
        print("❌ Could not load screenshot. Please check the screenshot file path.")
else:
    print("❌ Section-wise system not initialized. Please run the initialization cells first.")

print("\n" + "=" * 60)
print("🎯 VISION FEATURES SUMMARY:")
print("   📸 Full screenshot with highlighted elements sent to Gemini")
print("   🔍 Close-up crops of individual elements sent to Gemini")
print("   🤖 Enhanced prompts for multimodal analysis")
print("   📊 Vision status tracking in all evaluation results")
print("   🎨 Configurable image quality and size settings")

def upload_custom_data():
    """Upload and process custom UI data files for heuristic evaluation"""

    print("📁 Upload Your Custom UI Data Files")
    print("=" * 50)
    print("Please upload the following files:")
    print("1. coordinates.json - UI element positions and labels")
    print("2. element_info.json - Complete DOM data for elements")
    print("\nNote: Files should follow the same format as the sample data.")

    # Upload coordinates file
    print("\n📍 Upload coordinates.json:")
    coord_files = files.upload()

    # Upload element info file
    print("\n🏗️ Upload element_info.json:")
    element_files = files.upload()

    try:
        # Process uploaded files
        coord_filename = list(coord_files.keys())[0]
        element_filename = list(element_files.keys())[0]

        # Load the data
        with open(coord_filename, 'r') as f:
            custom_coordinates = json.load(f)

        with open(element_filename, 'r') as f:
            custom_element_info = json.load(f)

        print(f"\n✅ Files loaded successfully!")
        print(f"📊 Coordinates: {len(custom_coordinates)} elements")
        print(f"🏗️ Element info: {len(custom_element_info)} elements")

        # Initialize system with custom data
        print("\n🔄 Initializing system with your data...")
        heuristic_system.load_data(custom_coordinates, custom_element_info)

        print("\n🎉 Your custom data is now ready for heuristic evaluation!")
        print("Run the evaluation cells above to analyze your UI elements.")

        return True

    except Exception as e:
        print(f"❌ Error processing uploaded files: {str(e)}")
        print("Please check that your files are in the correct JSON format.")
        return False

# Create upload button
upload_button = widgets.Button(
    description='📁 Upload Custom Data',
    button_style='info',
    layout=widgets.Layout(width='200px')
)

upload_output = widgets.Output()

def on_upload_click(button):
    with upload_output:
        clear_output(wait=True)
        upload_custom_data()

upload_button.on_click(on_upload_click)

display(widgets.VBox([
    widgets.HTML("<h3>📁 Custom Data Upload</h3>"),
    widgets.HTML("<p>Upload your own coordinates.json and element_info.json files to analyze custom web pages.</p>"),
    upload_button,
    upload_output
]))